#!/usr/bin/env python3
"""
Test script to verify the ML cleaning fixes work correctly.
"""

import sys
import os
import json
import pandas as pd
import numpy as np

def test_basic_functionality():
    """Test basic pandas/numpy functionality."""
    print("🔧 Testing basic functionality...")
    
    try:
        # Test pandas
        df = pd.DataFrame({'x': [1, 2, 3], 'y': [4, 5, 6]})
        print(f"✅ pandas works: {len(df)} rows")
        
        # Test numpy
        arr = np.array([1, 2, 3])
        print(f"✅ numpy works: {len(arr)} elements")
        
        # Test the mad() replacement
        data = pd.Series([1, 2, 3, 4, 5, 100])
        std_val = data.std()
        median_val = data.median()
        outliers = (np.abs(data - median_val) > 3 * std_val).astype(int)
        print(f"✅ mad() replacement works: found {outliers.sum()} outliers")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality failed: {e}")
        return False

def test_simple_ml_cleaner():
    """Test the simple ML cleaner."""
    print("\n🤖 Testing Simple ML Cleaner...")
    
    try:
        from ML.simple_ml_cleaner import SimpleTouchDataCleaner
        
        # Create sample data
        sample_json = {
            "message": "gameData",
            "json": {
                "dataSet": "Coloring",
                "touchData": {
                    "1": [
                        {"x": 100, "y": 100, "time": 1000, "touchPhase": "Began", "fingerId": 0, "color": "Red", "completionPerc": 0.0},
                        {"x": 110, "y": 105, "time": 1100, "touchPhase": "Moved", "fingerId": 0, "color": "Red", "completionPerc": 0.5},
                        {"x": 120, "y": 110, "time": 1200, "touchPhase": "Ended", "fingerId": 0, "color": "Red", "completionPerc": 1.0}
                    ]
                }
            }
        }
        
        # Save to temp file
        with open('temp_test.json', 'w') as f:
            json.dump(sample_json, f)
        
        # Test the cleaner
        cleaner = SimpleTouchDataCleaner()
        result = cleaner.process_json_file('temp_test.json')
        
        if result['status'] == 'success':
            stats = result['statistics']
            print(f"✅ Simple ML cleaner works!")
            print(f"   Original data points: {stats['original_data_points']}")
            print(f"   Enhanced data points: {stats['enhanced_data_points']}")
            print(f"   Features added: {stats['features_added']}")
            
            # Verify data preservation
            original_x = [100, 110, 120]
            enhanced_data = result['enhanced_data']
            enhanced_x = [point['x'] for point in enhanced_data['json']['touchData']['1']]
            
            if original_x == enhanced_x:
                print("✅ Data preservation verified!")
                return True
            else:
                print("❌ Data preservation failed!")
                return False
        else:
            print(f"❌ Simple ML cleaner failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Simple ML cleaner test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        if os.path.exists('temp_test.json'):
            os.remove('temp_test.json')

def test_feature_engineering():
    """Test the feature engineering component."""
    print("\n⚙️ Testing Feature Engineering...")
    
    try:
        from ML.feature_engineering import TouchFeatureEngineer
        
        # Create sample data
        data = pd.DataFrame({
            'Touchdata_id': [1, 1, 1, 2, 2],
            'event_index': [0, 1, 2, 0, 1],
            'x': [100, 110, 120, 200, 210],
            'y': [100, 105, 110, 200, 205],
            'time': [1000, 1100, 1200, 2000, 2100],
            'touchPhase': ['Began', 'Moved', 'Ended', 'Began', 'Ended'],
            'fingerId': [1, 1, 1, 2, 2]
        })
        
        # Test feature engineering
        feature_engineer = TouchFeatureEngineer()
        enhanced_data = feature_engineer.extract_all_features(data.copy())
        
        # Check that features were added
        original_cols = len(data.columns)
        enhanced_cols = len(enhanced_data.columns)
        features_added = enhanced_cols - original_cols
        
        print(f"✅ Feature engineering works!")
        print(f"   Original columns: {original_cols}")
        print(f"   Enhanced columns: {enhanced_cols}")
        print(f"   Features added: {features_added}")
        
        # Verify data preservation
        if data['x'].equals(enhanced_data['x']) and data['y'].equals(enhanced_data['y']):
            print("✅ Data preservation in feature engineering verified!")
            return True
        else:
            print("❌ Data preservation in feature engineering failed!")
            return False
            
    except Exception as e:
        print(f"❌ Feature engineering test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_file():
    """Test with a real JSON file if available."""
    print("\n📁 Testing with real file...")
    
    try:
        # Find a real JSON file
        json_files = [f for f in os.listdir('raw_JSONs') if f.startswith('Coloring_') and f.endswith('.json')]
        
        if not json_files:
            print("⚠️ No real JSON files found, skipping real file test")
            return True
        
        sample_file = f'raw_JSONs/{json_files[0]}'
        print(f"Testing with: {sample_file}")
        
        from ML.simple_ml_cleaner import SimpleTouchDataCleaner
        
        cleaner = SimpleTouchDataCleaner()
        result = cleaner.process_json_file(sample_file)
        
        if result['status'] == 'success':
            stats = result['statistics']
            print(f"✅ Real file processing works!")
            print(f"   File: {os.path.basename(sample_file)}")
            print(f"   Data points: {stats['original_data_points']}")
            print(f"   Sequences: {stats['sequences_processed']}")
            print(f"   Quality distribution: {stats['quality_distribution']}")
            return True
        else:
            print(f"❌ Real file processing failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Real file test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 ML CLEANING FIXES VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Simple ML Cleaner", test_simple_ml_cleaner),
        ("Feature Engineering", test_feature_engineering),
        ("Real File Processing", test_real_file)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The ML cleaning system is working correctly.")
        print("\nYou can now run:")
        print("python ML/ml_clean_coloring_data.py --input raw_JSONs --output enhanced_data")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
