#!/usr/bin/env python3
"""
Test Documentation: Quick verification that the enhanced CSV data works as described
"""

import pandas as pd
import numpy as np
from glob import glob
import os

def test_documentation_examples():
    """Test the examples from our documentation to ensure they work."""
    
    print("🧪 TESTING DOCUMENTATION EXAMPLES")
    print("=" * 40)
    
    # Test 1: Check if CSV files exist
    print("📁 Test 1: Checking for enhanced CSV files...")
    csv_files = glob('enhanced_data_CSVs/*.csv')
    
    if not csv_files:
        print("❌ No CSV files found! Please run convert_enhanced_json_to_csv.py first")
        return False
    
    print(f"✅ Found {len(csv_files)} CSV files")
    
    # Test 2: Load a sample file
    print("\n📖 Test 2: Loading sample file...")
    try:
        sample_file = csv_files[0]
        df = pd.read_csv(sample_file, comment='#')
        print(f"✅ Successfully loaded {len(df):,} rows with {len(df.columns)} columns")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return False
    
    # Test 3: Verify column structure
    print("\n🔍 Test 3: Verifying column structure...")
    
    # Expected original columns
    original_cols = [
        'Touchdata_id', 'event_index', 'x', 'y', 'time', 'touchPhase', 
        'fingerId', 'accx', 'accy', 'accz', 'color', 'zone', 'completionPerc'
    ]
    
    # Check if original columns exist
    missing_original = [col for col in original_cols if col not in df.columns]
    if missing_original:
        print(f"❌ Missing original columns: {missing_original}")
        return False
    
    # Check for key ML features
    key_ml_features = [
        'ml_quality_score', 'quality_tier', 'behavioral_pattern', 
        'anomaly_score', 'velocity', 'temporal_consistency', 'spatial_consistency'
    ]
    
    missing_ml = [col for col in key_ml_features if col not in df.columns]
    if missing_ml:
        print(f"❌ Missing ML features: {missing_ml}")
        return False
    
    print(f"✅ All key columns present ({len(df.columns)} total)")
    
    # Test 4: Verify data quality distribution
    print("\n📊 Test 4: Checking data quality distribution...")
    
    if 'quality_tier' in df.columns:
        quality_dist = df['quality_tier'].value_counts()
        print("Quality distribution:")
        for tier, count in quality_dist.items():
            print(f"  {tier}: {count:,} ({count/len(df)*100:.1f}%)")
    
    # Test 5: Test filtering examples from documentation
    print("\n🎯 Test 5: Testing filtering examples...")
    
    # High quality filter
    try:
        high_quality = df[
            (df['ml_quality_score'] >= 0.8) &
            (df['quality_tier'] == 'high') &
            (df['sequence_valid_pattern'] == 1)
        ]
        retention_rate = len(high_quality) / len(df) * 100
        print(f"✅ High quality filter: {len(high_quality):,} points ({retention_rate:.1f}% retention)")
    except Exception as e:
        print(f"❌ High quality filter failed: {e}")
        return False
    
    # Research grade filter
    try:
        research_grade = df[
            (df['ml_quality_score'] >= 0.85) &
            (df['temporal_consistency'] >= 0.9) &
            (df['spatial_consistency'] >= 0.6) &
            (df['anomaly_type'] == 'normal')
        ]
        retention_rate = len(research_grade) / len(df) * 100
        print(f"✅ Research grade filter: {len(research_grade):,} points ({retention_rate:.1f}% retention)")
    except Exception as e:
        print(f"❌ Research grade filter failed: {e}")
        return False
    
    # Test 6: Verify behavioral patterns
    print("\n🎭 Test 6: Checking behavioral patterns...")
    
    if 'behavioral_pattern' in df.columns:
        behavior_dist = df['behavioral_pattern'].value_counts()
        print("Behavioral patterns:")
        for pattern, count in behavior_dist.items():
            print(f"  {pattern}: {count:,} ({count/len(df)*100:.1f}%)")
    
    # Test 7: Test multi-file loading
    print("\n📁 Test 7: Testing multi-file loading...")
    
    try:
        # Load first 3 files as test
        test_files = csv_files[:3]
        all_data = []
        
        for csv_file in test_files:
            df_temp = pd.read_csv(csv_file, comment='#')
            all_data.append(df_temp)
        
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"✅ Combined {len(test_files)} files: {len(combined_df):,} total points")
        
    except Exception as e:
        print(f"❌ Multi-file loading failed: {e}")
        return False
    
    # Test 8: Verify metadata headers
    print("\n📋 Test 8: Checking metadata headers...")
    
    try:
        with open(sample_file, 'r') as f:
            first_lines = [f.readline().strip() for _ in range(5)]
        
        header_found = any(line.startswith('#') for line in first_lines)
        if header_found:
            print("✅ Metadata headers found")
        else:
            print("⚠️ No metadata headers found (but data still works)")
            
    except Exception as e:
        print(f"❌ Error checking headers: {e}")
    
    print("\n🎉 ALL TESTS PASSED!")
    print("Your documentation examples work correctly!")
    
    return True

def show_quick_stats():
    """Show quick statistics about the enhanced dataset."""
    
    print("\n📊 QUICK DATASET STATISTICS")
    print("=" * 30)
    
    csv_files = glob('enhanced_data_CSVs/*.csv')
    if not csv_files:
        print("❌ No CSV files found")
        return
    
    # Load one file for stats
    df = pd.read_csv(csv_files[0], comment='#')
    
    print(f"📁 Total CSV files available: {len(csv_files)}")
    print(f"📊 Sample file statistics:")
    print(f"  • Touch points: {len(df):,}")
    print(f"  • Features per point: {len(df.columns)}")
    print(f"  • Touch sequences: {df['Touchdata_id'].nunique()}")
    print(f"  • Average quality score: {df['ml_quality_score'].mean():.3f}")
    
    if 'quality_tier' in df.columns:
        high_quality_pct = (df['quality_tier'] == 'high').sum() / len(df) * 100
        print(f"  • High quality data: {high_quality_pct:.1f}%")
    
    if 'behavioral_pattern' in df.columns:
        main_behavior = df['behavioral_pattern'].value_counts().index[0]
        main_behavior_pct = df['behavioral_pattern'].value_counts().iloc[0] / len(df) * 100
        print(f"  • Main behavior: {main_behavior} ({main_behavior_pct:.1f}%)")

if __name__ == "__main__":
    # Run tests
    success = test_documentation_examples()
    
    if success:
        show_quick_stats()
        print("\n🎯 Ready to use the documentation!")
        print("📖 Open README.md for overview")
        print("📓 Open ml_features_explanation.ipynb for interactive tutorial")
    else:
        print("\n❌ Some tests failed. Please check your setup.")
