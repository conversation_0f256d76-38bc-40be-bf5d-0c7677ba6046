#!/usr/bin/env python3
"""
Comprehensive Analysis of Enhanced Coloring Dataset
Evaluates the performance improvements from processing 47 JSON files
"""

import json
import os
import pandas as pd
import numpy as np
from collections import defaultdict
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_enhanced_dataset(enhanced_dir="enhanced_data_complete"):
    """Analyze all enhanced files and generate comprehensive performance metrics."""
    
    print("🔍 COMPREHENSIVE ENHANCED DATASET ANALYSIS")
    print("=" * 60)
    
    # Initialize aggregated statistics
    total_stats = {
        'files_processed': 0,
        'total_sequences': 0,
        'total_touch_points': 0,
        'quality_distribution': defaultdict(int),
        'behavioral_patterns': defaultdict(int),
        'anomaly_counts': defaultdict(int),
        'temporal_span': {'earliest': None, 'latest': None},
        'users': set(),
        'feature_completeness': defaultdict(int)
    }
    
    file_details = []
    
    # Process each enhanced file
    for filename in os.listdir(enhanced_dir):
        if filename.endswith('.json') and filename.startswith('enhanced_'):
            filepath = os.path.join(enhanced_dir, filename)
            
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                # Extract metadata
                ml_metadata = data.get('ml_metadata', {})
                
                # File-level statistics
                file_stat = {
                    'filename': filename,
                    'sequences': ml_metadata.get('total_sequences', 0),
                    'touch_points': ml_metadata.get('total_touch_points', 0),
                    'quality_dist': ml_metadata.get('quality_distribution', {}),
                    'behavioral_patterns': ml_metadata.get('behavioral_patterns', {}),
                    'anomaly_count': ml_metadata.get('anomaly_count', 0),
                    'processing_time': ml_metadata.get('processing_timestamp', ''),
                    'user_id': filename.split('_')[-1].replace('.json', '') if '_' in filename else 'unknown'
                }
                
                file_details.append(file_stat)
                
                # Aggregate statistics
                total_stats['files_processed'] += 1
                total_stats['total_sequences'] += file_stat['sequences']
                total_stats['total_touch_points'] += file_stat['touch_points']
                total_stats['users'].add(file_stat['user_id'])
                
                # Quality distribution
                for quality, count in file_stat['quality_dist'].items():
                    total_stats['quality_distribution'][quality] += count
                
                # Behavioral patterns
                for pattern, count in file_stat['behavioral_patterns'].items():
                    total_stats['behavioral_patterns'][pattern] += count
                
                # Anomaly tracking
                total_stats['anomaly_counts']['total'] += file_stat['anomaly_count']
                total_stats['anomaly_counts']['normal'] += (file_stat['touch_points'] - file_stat['anomaly_count'])
                
                # Extract date from filename for temporal analysis
                try:
                    date_str = filename.split('_')[1] + ' ' + filename.split('_')[2]
                    file_date = datetime.strptime(date_str, '%Y-%m-%d %H_%M_%S.%f')
                    
                    if total_stats['temporal_span']['earliest'] is None or file_date < total_stats['temporal_span']['earliest']:
                        total_stats['temporal_span']['earliest'] = file_date
                    if total_stats['temporal_span']['latest'] is None or file_date > total_stats['temporal_span']['latest']:
                        total_stats['temporal_span']['latest'] = file_date
                except:
                    pass
                
                # Analyze feature completeness by sampling touch data
                touch_data = data.get('json', {}).get('touchData', {})
                if touch_data:
                    # Sample first sequence to check feature completeness
                    first_seq = list(touch_data.values())[0]
                    if first_seq and len(first_seq) > 0:
                        first_point = first_seq[0]
                        
                        # Count ML features present
                        ml_features = [
                            'ml_quality_score', 'quality_tier', 'behavioral_pattern',
                            'anomaly_score', 'velocity', 'acceleration', 'distance',
                            'usage_recommendations', 'spatial_consistency'
                        ]
                        
                        for feature in ml_features:
                            if feature in first_point:
                                total_stats['feature_completeness'][feature] += 1
                
            except Exception as e:
                print(f"❌ Error processing {filename}: {e}")
                continue
    
    # Generate comprehensive report
    print_comprehensive_report(total_stats, file_details)
    
    return total_stats, file_details

def print_comprehensive_report(stats, file_details):
    """Print detailed performance analysis report."""
    
    print(f"\n📊 DATASET OVERVIEW")
    print(f"Files Successfully Processed: {stats['files_processed']}")
    print(f"Unique Users: {len(stats['users'])}")
    print(f"Total Touch Sequences: {stats['total_sequences']:,}")
    print(f"Total Touch Points: {stats['total_touch_points']:,}")
    
    if stats['temporal_span']['earliest'] and stats['temporal_span']['latest']:
        span = stats['temporal_span']['latest'] - stats['temporal_span']['earliest']
        print(f"Temporal Span: {span.days} days ({stats['temporal_span']['earliest'].strftime('%Y-%m-%d')} to {stats['temporal_span']['latest'].strftime('%Y-%m-%d')})")
    
    print(f"\n🎯 QUALITY ASSESSMENT PERFORMANCE")
    total_quality_points = sum(stats['quality_distribution'].values())
    for quality, count in stats['quality_distribution'].items():
        percentage = (count / total_quality_points * 100) if total_quality_points > 0 else 0
        print(f"  {quality.capitalize()}: {count:,} ({percentage:.1f}%)")
    
    print(f"\n🎭 BEHAVIORAL PATTERN CLASSIFICATION")
    total_behavioral_points = sum(stats['behavioral_patterns'].values())
    for pattern, count in stats['behavioral_patterns'].items():
        percentage = (count / total_behavioral_points * 100) if total_behavioral_points > 0 else 0
        print(f"  {pattern.capitalize()}: {count:,} ({percentage:.1f}%)")
    
    print(f"\n🚨 ANOMALY DETECTION EFFECTIVENESS")
    total_anomaly_points = stats['anomaly_counts']['total'] + stats['anomaly_counts']['normal']
    anomaly_rate = (stats['anomaly_counts']['total'] / total_anomaly_points * 100) if total_anomaly_points > 0 else 0
    print(f"  Normal Points: {stats['anomaly_counts']['normal']:,}")
    print(f"  Anomaly Points: {stats['anomaly_counts']['total']:,}")
    print(f"  Anomaly Detection Rate: {anomaly_rate:.2f}%")
    
    print(f"\n🔧 FEATURE ENGINEERING COMPLETENESS")
    for feature, count in stats['feature_completeness'].items():
        coverage = (count / stats['files_processed'] * 100) if stats['files_processed'] > 0 else 0
        print(f"  {feature}: {count}/{stats['files_processed']} files ({coverage:.1f}%)")
    
    print(f"\n📈 TOP PERFORMING FILES (by touch points)")
    sorted_files = sorted(file_details, key=lambda x: x['touch_points'], reverse=True)[:5]
    for i, file_info in enumerate(sorted_files, 1):
        print(f"  {i}. {file_info['filename'][:50]}... - {file_info['touch_points']:,} points, {file_info['sequences']} sequences")

if __name__ == "__main__":
    stats, details = analyze_enhanced_dataset()
    
    print(f"\n✅ ANALYSIS COMPLETE!")
    print(f"Enhanced dataset ready for advanced ML analysis, data visualization, and research applications.")
