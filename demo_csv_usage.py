#!/usr/bin/env python3
"""
Demonstration: How to Use Enhanced Coloring CSV Data
Shows practical examples of loading and filtering the converted CSV files
"""

import pandas as pd
import numpy as np
import os
from glob import glob

def demo_csv_usage():
    """Demonstrate how to load and use the enhanced CSV data."""
    
    print("📊 ENHANCED COLORING CSV DATA - USAGE DEMONSTRATION")
    print("=" * 60)
    
    # 1. Load a single CSV file
    print("\n1️⃣ LOADING A SINGLE CSV FILE")
    print("-" * 30)
    
    csv_files = glob("enhanced_data_CSVs/*.csv")
    if not csv_files:
        print("❌ No CSV files found. Run convert_enhanced_json_to_csv.py first!")
        return
    
    sample_file = csv_files[0]
    print(f"Loading: {os.path.basename(sample_file)}")
    
    # Load CSV (comment='#' skips metadata header)
    df = pd.read_csv(sample_file, comment='#')
    
    print(f"✅ Loaded {len(df)} rows with {len(df.columns)} columns")
    print(f"Unique sequences: {df['Touchdata_id'].nunique()}")
    print(f"Date range: {df['time'].min():.1f} - {df['time'].max():.1f}")
    
    # 2. Show column structure
    print("\n2️⃣ COLUMN STRUCTURE")
    print("-" * 20)
    
    original_cols = ['Touchdata_id', 'event_index', 'x', 'y', 'time', 'touchPhase', 
                    'fingerId', 'accx', 'accy', 'accz', 'color', 'zone', 'completionPerc']
    ml_cols = [col for col in df.columns if col not in original_cols and col != 'source_file']
    
    print(f"Original fields: {len(original_cols)}")
    print(f"ML-generated features: {len(ml_cols)}")
    print(f"Total columns: {len(df.columns)}")
    
    # 3. Apply quality filtering examples
    print("\n3️⃣ QUALITY FILTERING EXAMPLES")
    print("-" * 30)
    
    # High quality data
    high_quality = df[
        (df['ml_quality_score'] >= 0.8) &
        (df['quality_tier'] == 'high') &
        (df['sequence_valid_pattern'] == 1)
    ]
    print(f"High quality data: {len(high_quality)} rows ({len(high_quality)/len(df)*100:.1f}%)")
    
    # Timing analysis ready data
    timing_ready = df[
        (df['temporal_consistency'] >= 0.8) &
        (df['time_gap_outlier'] == 0) &
        (df['ml_quality_score'] >= 0.8)
    ]
    print(f"Timing analysis ready: {len(timing_ready)} rows ({len(timing_ready)/len(df)*100:.1f}%)")
    
    # Spatial analysis ready data
    spatial_ready = df[
        (df['spatial_consistency'] >= 0.5) &
        (df['distance_outlier'] == 0) &
        (df['velocity_outlier'] == 0)
    ]
    print(f"Spatial analysis ready: {len(spatial_ready)} rows ({len(spatial_ready)/len(df)*100:.1f}%)")
    
    # 4. Show quality distribution
    print("\n4️⃣ QUALITY DISTRIBUTION")
    print("-" * 25)
    
    quality_dist = df['quality_tier'].value_counts()
    for tier, count in quality_dist.items():
        print(f"{tier.capitalize()}: {count} ({count/len(df)*100:.1f}%)")
    
    # 5. Show behavioral patterns
    print("\n5️⃣ BEHAVIORAL PATTERNS")
    print("-" * 25)
    
    behavior_dist = df['behavioral_pattern'].value_counts()
    for pattern, count in behavior_dist.items():
        print(f"{pattern.capitalize()}: {count} ({count/len(df)*100:.1f}%)")
    
    # 6. Load multiple files
    print("\n6️⃣ LOADING MULTIPLE CSV FILES")
    print("-" * 30)
    
    all_data = []
    for i, csv_file in enumerate(csv_files[:3]):  # Load first 3 files as example
        df_temp = pd.read_csv(csv_file, comment='#')
        all_data.append(df_temp)
        print(f"File {i+1}: {len(df_temp)} rows from {os.path.basename(csv_file)}")
    
    # Combine all data
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"✅ Combined dataset: {len(combined_df)} rows from {len(all_data)} files")
    
    # 7. Advanced filtering example
    print("\n7️⃣ ADVANCED FILTERING EXAMPLE")
    print("-" * 35)
    
    research_grade = combined_df[
        (combined_df['ml_quality_score'] >= 0.85) &
        (combined_df['temporal_consistency'] >= 0.9) &
        (combined_df['spatial_consistency'] >= 0.6) &
        (combined_df['anomaly_type'] == 'normal') &
        (combined_df['behavioral_pattern'].isin(['drag', 'tap', 'hold']))
    ]
    
    print(f"Research-grade dataset: {len(research_grade)} rows")
    print(f"Retention rate: {len(research_grade)/len(combined_df)*100:.1f}%")
    print(f"Unique users: {research_grade['source_file'].nunique()}")
    print(f"Unique sequences: {research_grade['Touchdata_id'].nunique()}")
    
    # 8. Export filtered data
    print("\n8️⃣ EXPORT FILTERED DATA")
    print("-" * 25)
    
    output_file = "research_grade_coloring_data.csv"
    research_grade.to_csv(output_file, index=False)
    print(f"✅ Exported research-grade data to: {output_file}")
    
    # 9. Summary statistics
    print("\n9️⃣ SUMMARY STATISTICS")
    print("-" * 22)
    
    print(f"Quality Score - Mean: {combined_df['ml_quality_score'].mean():.3f}")
    print(f"Anomaly Score - Mean: {combined_df['anomaly_score'].mean():.3f}")
    print(f"Velocity - Mean: {combined_df['velocity'].mean():.1f} px/s")
    print(f"Distance - Mean: {combined_df['distance'].mean():.1f} px")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Use pd.read_csv('enhanced_data_CSVs/filename.csv', comment='#') to load files")
    print("2. Apply filtering criteria based on your analysis needs")
    print("3. Combine multiple files using pd.concat() for larger datasets")
    print("4. Export filtered subsets for specific research questions")
    
    return combined_df

if __name__ == "__main__":
    demo_data = demo_csv_usage()
