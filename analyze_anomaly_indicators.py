#!/usr/bin/env python3
"""
Analyze Anomaly Indicators in Enhanced Coloring Data
Examines specific flags, metrics, and criteria for identifying problematic data
"""

import json
import os
import pandas as pd
import numpy as np
from collections import defaultdict

def analyze_anomaly_indicators():
    """Analyze anomaly detection indicators across enhanced dataset."""
    
    print("🔍 ANOMALY INDICATORS ANALYSIS")
    print("=" * 50)
    
    # Load sample enhanced files
    enhanced_dir = "enhanced_data_complete"
    enhanced_files = [f for f in os.listdir(enhanced_dir) if f.endswith('.json') and f.startswith('enhanced_')]
    
    all_data_points = []
    
    # Collect data points from multiple files
    for i, filename in enumerate(enhanced_files[:5]):  # Analyze first 5 files
        filepath = os.path.join(enhanced_dir, filename)
        
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        touch_data = data['json']['touchData']
        
        for seq_id, sequence in touch_data.items():
            for point in sequence:
                point['file_source'] = filename
                point['sequence_id'] = seq_id
                all_data_points.append(point)
    
    print(f"Analyzing {len(all_data_points)} data points from {len(enhanced_files[:5])} files")
    
    # Analyze different categories
    analyze_quality_indicators(all_data_points)
    analyze_anomaly_detection(all_data_points)
    analyze_sequence_validation(all_data_points)
    analyze_outlier_flags(all_data_points)
    analyze_behavioral_anomalies(all_data_points)

def analyze_quality_indicators(data_points):
    """Analyze quality assessment indicators."""
    print("\n🎯 1. QUALITY ASSESSMENT INDICATORS")
    print("-" * 40)
    
    # Group by quality tier
    quality_groups = defaultdict(list)
    for point in data_points:
        tier = point.get('quality_tier', 'unknown')
        quality_groups[tier].append(point)
    
    for tier, points in quality_groups.items():
        if not points:
            continue
            
        scores = [p.get('ml_quality_score', 0) for p in points]
        completeness = [p.get('sequence_completeness', 0) for p in points]
        temporal = [p.get('temporal_consistency', 0) for p in points]
        spatial = [p.get('spatial_consistency', 0) for p in points]
        
        print(f"\n{tier.upper()} Quality ({len(points)} points):")
        print(f"  ML Quality Score: {np.mean(scores):.3f} ± {np.std(scores):.3f}")
        print(f"  Sequence Completeness: {np.mean(completeness):.3f} ± {np.std(completeness):.3f}")
        print(f"  Temporal Consistency: {np.mean(temporal):.3f} ± {np.std(temporal):.3f}")
        print(f"  Spatial Consistency: {np.mean(spatial):.3f} ± {np.std(spatial):.3f}")
        
        # Show example of low quality indicators
        if tier == 'low':
            low_quality_examples = [p for p in points if p.get('ml_quality_score', 1) < 0.3]
            if low_quality_examples:
                example = low_quality_examples[0]
                print(f"  Example Low Quality Point:")
                print(f"    Touch Phase: {example.get('touchPhase')}")
                print(f"    Sequence Valid: {example.get('sequence_valid_pattern')}")
                print(f"    Has Began: {example.get('has_began')}")
                print(f"    Has Ended: {example.get('has_ended')}")

def analyze_anomaly_detection(data_points):
    """Analyze anomaly detection results."""
    print("\n🚨 2. ANOMALY DETECTION INDICATORS")
    print("-" * 40)
    
    # Group by anomaly type
    anomaly_groups = defaultdict(list)
    for point in data_points:
        anom_type = point.get('anomaly_type', 'unknown')
        anomaly_groups[anom_type].append(point)
    
    for anom_type, points in anomaly_groups.items():
        if not points:
            continue
            
        scores = [p.get('anomaly_score', 0) for p in points]
        confidence = [p.get('anomaly_confidence', 0) for p in points]
        
        print(f"\n{anom_type.upper()} Points ({len(points)} points):")
        print(f"  Anomaly Score: {np.mean(scores):.3f} ± {np.std(scores):.3f}")
        print(f"  Anomaly Confidence: {np.mean(confidence):.3f} ± {np.std(confidence):.3f}")
        
        # Show characteristics of outliers
        if anom_type == 'outlier':
            high_anomaly = [p for p in points if p.get('anomaly_score', 0) > 0.8]
            if high_anomaly:
                example = high_anomaly[0]
                print(f"  High Anomaly Example:")
                print(f"    Position: ({example.get('x', 0):.1f}, {example.get('y', 0):.1f})")
                print(f"    Velocity: {example.get('velocity', 0):.3f}")
                print(f"    Distance: {example.get('distance', 0):.3f}")
                print(f"    Zone: {example.get('zone', 'unknown')}")

def analyze_sequence_validation(data_points):
    """Analyze sequence validation flags."""
    print("\n✅ 3. SEQUENCE VALIDATION INDICATORS")
    print("-" * 40)
    
    # Count validation flags
    valid_patterns = sum(1 for p in data_points if p.get('sequence_valid_pattern', 0) == 1)
    invalid_patterns = sum(1 for p in data_points if p.get('sequence_valid_pattern', 0) == 0)
    
    has_began = sum(1 for p in data_points if p.get('has_began', 0) == 1)
    has_ended = sum(1 for p in data_points if p.get('has_ended', 0) == 1)
    has_canceled = sum(1 for p in data_points if p.get('has_canceled', 0) == 1)
    
    print(f"Valid Sequence Patterns: {valid_patterns} ({valid_patterns/len(data_points)*100:.1f}%)")
    print(f"Invalid Sequence Patterns: {invalid_patterns} ({invalid_patterns/len(data_points)*100:.1f}%)")
    print(f"Has Began: {has_began} ({has_began/len(data_points)*100:.1f}%)")
    print(f"Has Ended: {has_ended} ({has_ended/len(data_points)*100:.1f}%)")
    print(f"Has Canceled: {has_canceled} ({has_canceled/len(data_points)*100:.1f}%)")
    
    # Analyze invalid patterns
    invalid_examples = [p for p in data_points if p.get('sequence_valid_pattern', 0) == 0]
    if invalid_examples:
        print(f"\nInvalid Pattern Analysis ({len(invalid_examples)} points):")
        
        # Group by touch phase to see common invalid patterns
        phase_counts = defaultdict(int)
        for point in invalid_examples:
            phase_counts[point.get('touchPhase', 'unknown')] += 1
        
        print("  Touch Phase Distribution in Invalid Sequences:")
        for phase, count in sorted(phase_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"    {phase}: {count} ({count/len(invalid_examples)*100:.1f}%)")

def analyze_outlier_flags(data_points):
    """Analyze specific outlier detection flags."""
    print("\n⚠️  4. OUTLIER DETECTION FLAGS")
    print("-" * 40)
    
    velocity_outliers = sum(1 for p in data_points if p.get('velocity_outlier', 0) == 1)
    distance_outliers = sum(1 for p in data_points if p.get('distance_outlier', 0) == 1)
    time_gap_outliers = sum(1 for p in data_points if p.get('time_gap_outlier', 0) == 1)
    
    print(f"Velocity Outliers: {velocity_outliers} ({velocity_outliers/len(data_points)*100:.1f}%)")
    print(f"Distance Outliers: {distance_outliers} ({distance_outliers/len(data_points)*100:.1f}%)")
    print(f"Time Gap Outliers: {time_gap_outliers} ({time_gap_outliers/len(data_points)*100:.1f}%)")
    
    # Analyze extreme values
    velocities = [p.get('velocity', 0) for p in data_points if p.get('velocity', 0) > 0]
    distances = [p.get('distance', 0) for p in data_points if p.get('distance', 0) > 0]
    time_diffs = [p.get('time_diff', 0) for p in data_points if p.get('time_diff', 0) > 0]
    
    if velocities:
        print(f"\nVelocity Statistics:")
        print(f"  Mean: {np.mean(velocities):.3f}, Max: {np.max(velocities):.3f}")
        print(f"  95th percentile: {np.percentile(velocities, 95):.3f}")
        
    if distances:
        print(f"\nDistance Statistics:")
        print(f"  Mean: {np.mean(distances):.3f}, Max: {np.max(distances):.3f}")
        print(f"  95th percentile: {np.percentile(distances, 95):.3f}")

def analyze_behavioral_anomalies(data_points):
    """Analyze behavioral pattern anomalies."""
    print("\n🎭 5. BEHAVIORAL ANOMALY INDICATORS")
    print("-" * 40)
    
    # Group by behavioral pattern
    pattern_groups = defaultdict(list)
    for point in data_points:
        pattern = point.get('behavioral_pattern', 'unknown')
        pattern_groups[pattern].append(point)
    
    for pattern, points in pattern_groups.items():
        if not points:
            continue
            
        confidence = [p.get('user_intent_confidence', 0) for p in points]
        interaction_styles = [p.get('interaction_style', 'unknown') for p in points]
        
        print(f"\n{pattern.upper()} Pattern ({len(points)} points):")
        print(f"  User Intent Confidence: {np.mean(confidence):.3f} ± {np.std(confidence):.3f}")
        
        # Count interaction styles
        style_counts = defaultdict(int)
        for style in interaction_styles:
            style_counts[style] += 1
        
        print("  Interaction Styles:")
        for style, count in style_counts.items():
            print(f"    {style}: {count} ({count/len(points)*100:.1f}%)")

if __name__ == "__main__":
    analyze_anomaly_indicators()
