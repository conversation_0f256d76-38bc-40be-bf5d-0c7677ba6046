2025-05-30 22:53:59,343 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 22:53:59,343 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 22:53:59,343 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 22:53:59,365 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 22:53:59,365 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 22:53:59,365 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 22:53:59,365 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 22:54:00,632 - __main__ - ERROR - Failed to initialize ML cleaner: string indices must be integers, not 'str'
2025-05-30 22:55:25,491 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 22:55:25,492 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 22:55:25,492 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 22:55:25,498 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 22:55:25,498 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 22:55:25,498 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 22:55:25,498 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 22:55:26,645 - __main__ - ERROR - Failed to initialize ML cleaner: string indices must be integers, not 'str'
2025-05-30 23:03:30,366 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:03:30,366 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:03:30,367 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:03:30,387 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:03:30,388 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:03:30,388 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:03:30,388 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:03:31,545 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:03:31,613 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:03:31,613 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:03:31,620 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:03:31,620 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:03:31,621 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:03:31,628 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:03:31,628 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:03:31,628 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:03:31,628 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:03:31,866 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:03:31,913 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:03:31,913 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:03:31,918 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:31,921 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:31,921 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:31,921 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:31,921 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:31,930 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:31,937 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:31,941 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:31,956 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:31,956 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:03:31,959 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:31,959 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:31,960 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:31,960 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:31,965 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:31,971 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:31,974 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:31,984 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:31,984 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:03:31,993 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:31,993 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:31,993 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:31,993 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,001 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,008 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,012 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,027 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,028 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:32,034 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,034 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,034 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,034 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,046 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,057 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,060 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,087 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,088 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:03:32,095 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,095 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,095 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,095 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,103 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,111 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,115 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,133 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,133 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:03:32,141 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,141 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,141 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,141 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,151 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,160 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,164 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,187 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,187 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:32,194 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,194 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,194 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,194 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,200 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,205 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,209 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,219 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,219 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:03:32,221 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,221 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,221 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,222 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,226 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,231 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,234 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,240 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,241 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:32,241 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,242 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,242 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,242 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,245 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,249 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,252 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,254 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json: Cannot set a DataFrame with multiple columns to the single column sequence_valid_pattern
2025-05-30 23:03:32,254 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:03:32,258 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,258 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,258 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,258 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,263 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,268 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,271 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,279 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,279 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:03:32,292 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,292 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,292 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,293 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,305 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,316 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,321 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,349 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,349 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:03:32,356 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,356 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,356 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,356 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,362 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,368 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,372 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,384 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,385 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 0/12 files processed successfully
2025-05-30 23:06:01,335 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:06:01,335 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:06:01,335 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:06:01,341 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:06:01,341 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:06:01,341 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:06:01,341 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:06:02,485 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:06:02,536 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:06:02,537 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:06:02,541 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:06:02,541 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:06:02,541 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:06:02,548 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:06:02,548 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:06:02,548 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:06:02,548 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:06:02,782 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:06:02,828 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:06:02,828 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:06:02,834 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:02,836 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:02,837 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:02,837 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:02,837 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:02,846 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:02,853 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:02,856 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:02,888 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:02,888 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:02,888 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:02,912 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:02,912 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:02,930 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:02,930 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,035 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,038 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,039 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,043 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:03,044 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:03,047 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,047 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,047 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,047 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,053 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,058 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,061 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,081 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,081 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,081 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,095 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,095 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:03,106 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:03,106 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,213 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,218 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,218 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,226 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:03,227 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:03,236 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,236 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,236 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,236 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,244 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,293 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,298 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,336 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,336 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,337 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,360 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,360 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:03,379 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:03,379 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,503 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,522 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,524 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,555 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:03,556 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:03,563 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,563 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,563 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,563 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,575 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,585 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,589 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,646 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,647 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,647 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,694 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,694 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:03,731 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:03,731 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,847 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,859 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,860 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,882 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:03,883 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:03,889 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,889 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,889 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,889 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,898 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,906 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,909 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,946 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,946 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,946 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,976 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,976 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,000 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,000 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,115 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,127 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,129 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,151 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:04,152 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:04,160 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,160 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,160 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,160 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,171 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,180 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,184 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,232 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,232 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,232 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,273 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,273 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,307 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,307 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,426 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,441 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,443 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,470 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:04,471 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:04,477 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,477 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,477 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,478 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,484 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,489 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,493 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,509 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,509 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,509 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,523 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,523 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,533 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,533 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,650 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,662 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,663 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,685 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:04,686 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:04,689 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,689 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,689 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,689 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,693 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,698 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,702 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,714 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,714 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,714 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,723 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,723 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,730 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,730 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,836 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,839 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,840 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,848 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:04,848 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:04,849 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,849 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,849 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,849 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,853 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,857 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,860 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,865 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,865 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,865 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,868 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,868 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,869 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,870 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,007 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,008 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,008 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,009 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:05,009 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:05,012 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:05,012 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:05,012 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:05,012 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:05,018 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:05,023 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:05,026 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:05,040 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:05,041 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:05,041 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:05,052 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:05,052 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:05,059 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:05,060 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,169 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,174 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,175 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,185 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:05,186 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:05,200 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:05,200 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:05,200 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:05,200 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:05,214 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:05,225 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:05,231 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:05,289 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:05,289 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:05,289 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:05,346 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:05,346 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:05,391 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:05,392 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,526 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,554 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,556 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,604 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:05,606 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:05,612 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:05,612 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:05,612 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:05,612 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:05,619 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:05,624 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:05,629 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:05,652 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:05,652 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:05,652 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:05,671 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:05,671 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:05,685 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:05,685 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,805 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,816 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,817 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,837 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:06,083 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:07:21,785 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:07:21,785 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:07:21,785 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:07:21,791 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:07:21,792 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:07:21,792 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:07:21,792 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:07:23,004 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:07:23,058 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:07:23,058 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:07:23,063 - __main__ - INFO - Processing single file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:07:23,063 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:07:23,066 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:07:23,066 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:07:23,067 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:07:23,067 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:07:23,076 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:07:23,085 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:07:23,088 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:07:23,124 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:07:23,124 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:07:23,124 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:07:23,149 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:07:23,149 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:07:23,169 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:07:23,169 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:07:23,317 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:07:23,320 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:07:23,321 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:07:23,326 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: test_enhanced.json
2025-05-30 23:09:02,062 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:09:02,063 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:09:02,063 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:09:02,069 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:09:02,069 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:09:02,069 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:09:02,069 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:09:03,043 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:09:03,093 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:09:03,093 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:09:03,098 - __main__ - INFO - Processing single file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:03,098 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:03,100 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:03,100 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:03,100 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:03,100 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:03,110 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:03,117 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:03,120 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:03,154 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:03,154 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:03,154 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:03,177 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:03,177 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:03,196 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:03,196 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:03,300 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:03,303 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:03,304 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:03,308 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: test_enhanced.json
2025-05-30 23:09:22,848 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:09:22,849 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:09:22,849 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:09:22,855 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:09:22,855 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:09:22,855 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:09:22,855 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:09:23,873 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:09:23,923 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:09:23,923 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:09:23,926 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:09:23,927 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:09:23,927 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:09:23,933 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:09:23,933 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:09:23,933 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:09:23,933 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:09:24,168 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:09:24,216 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:09:24,216 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:09:24,220 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:24,222 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,222 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,222 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,222 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,233 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,240 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,243 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,276 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,276 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,276 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:24,299 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:24,299 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:24,318 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:24,318 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:24,420 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:24,423 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:24,424 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:24,429 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:24,429 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:24,432 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,432 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,432 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,433 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,438 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,443 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,448 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,468 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,468 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,468 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:24,482 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:24,482 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:24,493 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:24,493 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:24,602 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:24,606 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:24,607 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:24,615 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:24,616 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:24,625 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,625 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,625 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,625 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,633 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,640 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,645 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,675 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,675 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,675 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:24,700 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:24,700 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:24,720 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:24,720 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:24,843 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:24,861 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:24,862 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:24,893 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:24,895 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:24,902 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,902 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,902 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,902 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,914 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,924 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,929 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,987 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,987 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,987 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,035 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,035 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,073 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,073 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:25,190 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:25,203 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:25,204 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:25,225 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:25,226 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:25,233 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:25,233 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:25,233 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:25,233 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:25,241 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:25,250 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:25,254 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:25,291 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:25,291 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:25,291 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,322 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,322 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,347 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,347 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:25,464 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:25,477 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:25,478 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:25,500 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:25,502 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:25,509 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:25,510 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:25,510 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:25,510 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:25,520 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:25,530 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:25,534 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:25,583 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:25,583 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:25,583 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,624 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,624 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,658 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,658 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:25,779 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:25,795 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:25,796 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:25,824 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:25,825 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:25,832 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:25,832 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:25,832 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:25,832 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:25,838 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:25,843 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:25,848 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:25,864 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:25,864 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:25,865 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,877 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,877 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,887 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,887 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,006 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,018 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,020 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,041 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:26,042 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,045 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,045 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,045 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,045 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,050 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,054 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,058 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,070 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,070 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,070 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,079 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,079 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,086 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,086 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,193 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,197 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,197 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,204 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,205 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:26,206 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,206 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,206 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,206 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,209 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,213 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,216 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,221 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,221 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,221 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,224 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,224 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,225 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,225 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,317 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,317 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,318 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,318 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:26,319 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,322 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,322 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,322 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,322 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,328 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,333 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,337 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,352 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,352 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,352 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,364 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,364 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,372 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,372 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,485 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,490 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,491 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,500 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,501 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:26,515 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,515 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,515 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,515 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,528 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,539 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,545 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,602 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,603 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,603 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,658 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,658 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,702 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,703 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,836 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,864 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,865 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,915 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:26,916 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:26,922 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,923 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,923 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,923 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,930 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,936 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,940 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,964 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,964 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,964 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,982 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,982 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,996 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,996 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:27,112 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:27,123 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:27,124 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:27,143 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:27,390 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:10:23,590 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:10:23,590 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:10:23,590 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:10:23,596 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:10:23,596 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:10:23,596 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:10:23,596 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:10:24,539 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:10:24,587 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:10:24,588 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:10:24,591 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:10:24,591 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:10:24,592 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:10:24,598 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:10:24,598 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:10:24,598 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:10:24,598 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:10:24,832 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:10:24,878 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:10:24,878 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:10:24,882 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:24,884 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:24,884 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:24,884 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:24,884 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:24,893 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:24,901 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:24,904 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:24,936 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:24,936 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:24,936 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:24,958 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:24,958 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:24,975 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:24,975 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,076 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,079 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,080 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,084 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:25,085 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:25,088 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,088 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,088 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,088 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,094 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,099 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,102 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,122 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,122 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,122 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,136 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,136 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,146 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,146 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,253 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,258 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,259 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,266 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:25,267 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:25,275 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,276 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,276 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,276 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,284 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,291 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,295 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,325 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,325 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,325 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,348 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,348 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,367 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,367 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,488 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,505 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,507 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,537 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:25,539 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:25,545 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,545 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,545 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,546 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,557 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,567 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,571 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,628 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,628 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,628 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,675 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,675 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,712 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,712 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,827 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,839 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,841 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,862 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:25,863 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:25,869 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,870 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,870 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,870 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,878 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,886 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,890 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,926 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,926 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,926 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,956 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,956 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,980 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,980 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,094 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,106 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,108 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,129 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:26,130 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:26,138 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,138 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,138 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,138 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,148 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,157 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,161 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,210 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,210 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,210 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,250 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,250 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,283 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,283 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,402 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,418 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,420 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,447 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:26,448 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,455 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,455 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,455 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,455 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,460 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,466 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,470 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,486 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,486 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,486 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,498 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,498 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,507 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,508 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,625 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,637 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,638 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,659 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,660 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:26,662 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,663 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,663 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,663 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,667 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,672 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,675 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,687 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,687 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,687 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,696 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,696 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,702 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,702 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,806 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,810 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,811 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,817 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:26,818 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,819 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,819 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,819 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,819 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,822 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,826 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,829 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,834 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,834 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,834 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,836 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,836 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,838 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,838 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,926 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,927 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,927 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,928 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,928 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:26,931 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,931 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,931 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,931 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,937 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,942 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,945 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,959 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,959 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,959 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,970 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,970 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,978 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,978 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:27,086 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:27,091 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:27,092 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:27,101 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:27,102 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:27,116 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:27,116 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:27,116 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:27,116 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:27,129 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:27,139 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:27,145 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:27,202 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:27,202 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:27,202 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:27,256 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:27,256 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:27,299 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:27,299 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:27,429 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:27,457 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:27,459 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:27,507 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:27,508 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:27,514 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:27,514 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:27,514 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:27,514 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:27,520 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:27,526 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:27,530 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:27,553 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:27,553 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:27,553 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:27,571 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:27,571 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:27,584 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:27,584 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:27,697 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:27,708 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:27,709 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:27,728 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:27,970 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:24:06,287 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:24:06,287 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:24:06,287 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:24:06,312 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:24:06,312 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:24:06,312 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:24:06,312 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:24:07,521 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:24:07,590 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:24:07,590 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:24:07,598 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:24:07,598 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:24:07,598 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:24:07,604 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:24:07,604 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:24:07,604 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:24:07,604 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:24:07,838 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:24:07,886 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:24:07,886 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:24:07,891 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:07,894 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:07,894 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:07,894 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:07,894 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:07,903 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:07,911 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:07,914 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:07,946 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:07,946 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:07,946 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:07,969 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:07,970 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:07,987 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:07,987 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:08,093 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:08,096 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:08,097 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:08,101 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:08,102 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:24:08,105 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:08,105 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:08,105 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:08,105 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:08,111 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:08,116 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:08,119 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:08,140 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:08,140 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:08,140 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:08,154 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:08,154 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:08,165 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:08,165 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:08,274 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:08,279 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:08,280 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:08,325 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:24:08,332 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:24:08,348 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:08,348 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:08,348 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:08,349 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:08,360 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:08,369 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:08,374 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:08,408 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:08,408 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:08,408 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:08,432 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:08,432 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:08,451 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:08,451 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:08,601 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:08,619 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:08,620 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:08,652 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:24:08,653 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:08,659 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:08,659 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:08,660 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:08,660 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:08,671 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:08,682 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:08,686 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:08,745 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:08,745 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:08,745 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:08,792 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:08,792 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:08,830 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:08,830 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:08,947 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:08,960 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:08,961 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:08,982 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:08,983 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:24:08,990 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:08,990 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:08,990 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:08,991 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:08,999 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:09,007 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:09,011 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:09,049 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:09,049 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:09,049 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:09,081 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:09,081 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:09,104 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:09,104 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:09,221 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:09,234 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:09,236 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:09,258 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:24:09,259 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:24:09,268 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:09,268 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:09,268 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:09,268 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:09,279 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:09,288 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:09,293 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:09,342 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:09,342 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:09,342 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:09,384 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:09,384 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:09,417 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:09,417 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:09,537 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:09,552 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:09,554 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:09,582 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:24:09,583 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:09,590 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:09,590 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:09,590 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:09,590 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:09,595 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:09,601 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:09,605 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:09,621 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:09,621 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:09,621 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:09,634 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:09,634 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:09,644 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:09,644 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:09,762 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:09,774 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:09,775 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:09,797 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:09,798 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:24:09,800 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:09,800 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:09,800 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:09,801 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:09,805 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:09,811 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:09,814 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:09,826 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:09,827 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:09,827 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:09,835 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:09,835 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:09,842 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:09,842 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:09,949 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:09,952 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:09,954 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:09,961 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:24:09,961 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:09,962 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:09,962 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:09,962 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:09,962 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:09,966 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:09,970 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:09,973 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:09,978 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:09,978 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:09,978 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:09,980 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:09,981 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:09,982 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:09,982 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:10,072 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:10,073 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:10,074 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:10,074 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:24:10,075 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:24:10,078 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:10,078 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:10,078 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:10,079 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:10,083 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:10,089 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:10,093 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:10,108 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:10,108 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:10,108 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:10,118 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:10,118 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:10,127 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:10,127 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:10,237 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:10,242 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:10,243 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:10,252 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:24:10,253 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:24:10,266 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:10,266 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:10,266 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:10,266 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:10,279 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:10,289 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:10,295 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:10,352 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:10,352 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:10,352 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:10,407 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:10,407 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:10,452 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:10,452 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:10,584 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:10,612 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:10,614 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:10,663 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:24:10,665 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:24:10,671 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:24:10,671 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:24:10,671 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:24:10,671 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:24:10,678 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:24:10,684 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:24:10,688 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:24:10,712 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:24:10,712 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:24:10,712 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:24:10,730 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:24:10,730 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:24:10,745 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:24:10,745 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:24:10,860 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:24:10,871 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:24:10,872 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:24:10,891 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:24:11,141 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:25:21,102 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:25:21,103 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:25:21,103 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:25:21,109 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:25:21,109 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:25:21,109 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:25:21,109 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:25:22,264 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:25:22,314 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:25:22,315 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:25:22,320 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:25:22,320 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:25:22,321 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:25:22,327 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:25:22,328 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:25:22,328 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:25:22,328 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:25:22,566 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:25:22,614 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:25:22,614 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:25:22,618 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:22,621 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:22,622 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:22,622 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:22,622 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:22,631 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:22,638 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:22,642 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:22,675 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:22,675 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:22,675 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:22,698 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:22,698 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:22,716 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:22,716 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:22,885 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:22,887 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:22,933 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:22,933 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:22,936 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:22,936 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:22,936 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:22,936 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:22,943 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:22,948 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:22,951 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:22,971 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:22,971 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:22,971 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:22,984 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:22,984 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:22,996 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:22,996 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:23,105 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:23,109 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:23,187 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:23,188 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:25:23,197 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:23,197 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:23,197 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:23,198 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:23,206 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:23,213 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:23,217 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:23,248 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:23,248 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:23,248 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:23,277 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:23,277 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:23,297 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:23,297 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:23,423 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:23,442 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:23,758 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:25:23,759 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:23,767 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:23,767 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:23,767 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:23,767 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:23,781 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:23,792 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:23,796 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:23,855 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:23,855 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:23,855 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:23,903 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:23,903 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:23,941 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:23,941 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:24,059 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:24,071 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:24,293 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:24,294 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:25:24,301 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:24,301 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:24,301 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:24,301 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:24,311 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:24,318 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:24,323 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:24,361 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:24,361 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:24,361 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:24,392 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:24,392 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:24,416 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:24,416 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:24,534 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:24,547 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:24,772 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:25:24,773 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:25:24,781 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:24,782 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:24,782 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:24,782 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:24,793 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:24,802 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:24,807 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:24,856 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:24,856 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:24,856 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:24,897 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:24,897 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:24,931 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:24,931 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:25,052 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:25,068 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:25,354 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:25:25,355 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:25,362 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:25,362 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:25,362 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:25,362 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:25,368 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:25,375 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:25,379 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:25,443 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:25,443 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:25,443 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:25,464 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:25,464 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:25,474 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:25,474 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:25,591 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:25,603 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:25,816 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:25,817 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:25,820 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:25,820 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:25,820 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:25,820 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:25,826 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:25,830 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:25,834 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:25,846 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:25,846 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:25,846 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:25,854 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:25,854 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:25,861 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:25,861 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:25,968 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:25,972 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:26,037 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:26,038 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:26,039 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:26,039 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:26,039 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:26,039 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:26,042 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:26,046 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:26,049 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:26,054 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:26,054 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:26,054 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:26,057 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:26,057 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:26,059 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:26,059 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:26,148 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:26,149 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:26,151 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:25:26,151 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:26,155 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:26,155 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:26,155 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:26,155 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:26,160 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:26,165 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:26,168 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:26,182 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:26,182 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:26,183 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:26,193 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:26,193 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:26,201 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:26,201 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:26,312 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:26,317 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:26,407 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:26,407 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:25:26,421 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:26,421 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:26,421 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:26,422 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:26,434 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:26,446 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:26,451 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:26,584 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:26,584 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:26,584 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:26,639 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:26,639 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:26,683 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:26,683 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:26,816 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:26,843 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:27,343 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:25:27,345 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:25:27,351 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:27,351 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:27,351 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:27,351 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:27,358 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:27,365 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:27,368 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:27,392 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:27,393 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:27,393 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:27,411 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:27,411 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:27,425 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:27,425 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:27,540 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:27,552 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:27,741 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_fixed/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:25:29,100 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:25:43,965 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:25:43,965 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:25:43,965 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:25:43,971 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:25:43,971 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:25:43,972 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:25:43,972 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:25:45,180 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:25:45,229 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:25:45,229 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:25:45,233 - __main__ - INFO - Processing single file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:45,233 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:25:45,237 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:25:45,237 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:25:45,237 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:25:45,237 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:25:45,245 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:25:45,251 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:25:45,255 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:25:45,276 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:25:45,276 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:25:45,276 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:25:45,291 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:25:45,291 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:25:45,302 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:25:45,302 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:25:45,412 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:25:45,416 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:25:45,495 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_test.json
2025-05-30 23:26:06,879 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:26:06,879 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:26:06,879 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:26:06,885 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:26:06,885 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:26:06,885 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:26:06,885 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:26:07,857 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:26:07,907 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:26:07,908 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:26:07,912 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:26:07,912 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:26:07,913 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:26:07,919 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:26:07,919 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:26:07,919 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:26:07,919 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:26:08,153 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:26:08,199 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:26:08,199 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:26:08,203 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:08,206 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:08,207 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:08,207 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:08,207 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:08,216 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:08,223 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:08,227 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:08,260 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:08,260 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:08,260 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:08,283 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:08,283 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:08,301 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:08,301 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:08,404 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:08,406 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:08,451 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:08,452 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:26:08,454 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:08,455 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:08,455 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:08,455 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:08,461 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:08,467 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:08,470 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:08,491 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:08,491 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:08,491 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:08,504 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:08,504 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:08,515 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:08,515 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:08,624 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:08,629 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:08,708 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:26:08,708 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:26:08,723 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:08,723 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:08,723 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:08,724 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:08,774 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:08,789 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:08,794 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:08,830 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:08,830 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:08,830 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:08,856 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:08,856 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:08,875 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:08,876 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:08,999 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:09,017 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:09,332 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:26:09,334 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:09,341 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:09,341 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:09,341 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:09,341 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:09,354 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:09,365 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:09,369 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:09,428 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:09,429 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:09,429 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:09,476 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:09,477 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:09,515 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:09,515 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:09,634 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:09,647 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:09,925 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:09,926 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:26:09,934 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:09,934 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:09,934 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:09,934 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:09,943 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:09,951 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:09,956 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:10,018 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:10,019 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:10,019 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:10,053 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:10,053 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:10,077 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:10,078 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:10,193 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:10,206 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:10,434 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:26:10,435 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:26:10,443 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:10,444 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:10,444 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:10,444 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:10,456 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:10,466 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:10,470 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:10,520 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:10,520 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:10,520 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:10,562 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:10,562 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:10,596 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:10,596 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:10,716 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:10,732 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:11,018 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:26:11,020 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:11,026 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:11,027 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:11,027 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:11,027 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:11,032 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:11,038 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:11,043 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:11,059 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:11,059 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:11,059 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:11,071 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:11,072 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:11,081 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:11,081 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:11,197 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:11,209 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:11,422 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:11,423 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:26:11,426 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:11,426 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:11,426 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:11,426 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:11,431 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:11,436 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:11,440 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:11,451 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:11,451 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:11,451 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:11,461 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:11,461 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:11,467 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:11,467 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:11,573 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:11,577 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:11,643 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:26:11,643 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:11,644 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:11,644 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:11,644 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:11,644 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:11,648 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:11,652 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:11,655 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:11,660 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:11,660 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:11,660 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:11,663 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:11,663 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:11,664 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:11,664 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:11,754 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:11,754 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:11,757 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:11,757 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:26:11,760 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:11,760 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:11,760 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:11,760 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:11,765 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:11,771 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:11,775 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:11,789 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:11,789 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:11,789 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:11,800 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:11,800 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:11,808 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:11,808 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:11,917 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:11,922 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:12,012 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:26:12,013 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:26:12,027 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:12,027 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:12,027 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:12,028 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:12,040 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:12,051 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:12,056 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:12,190 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:12,190 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:12,190 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:12,245 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:12,245 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:12,289 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:12,289 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:12,421 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:12,448 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:12,997 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:26:12,999 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:26:13,005 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:13,006 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:13,006 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:13,006 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:13,012 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:13,019 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:13,023 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:13,047 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:13,047 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:13,047 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:13,065 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:13,065 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:13,079 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:13,079 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:13,193 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:13,206 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:13,393 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_ml_complete/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:26:14,759 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:26:34,929 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:26:34,929 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:26:34,929 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:26:34,935 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:26:34,935 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:26:34,935 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:26:34,935 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:26:35,907 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:26:35,957 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:26:35,957 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:26:35,961 - __main__ - INFO - Processing single file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:35,961 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:26:35,963 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:26:35,964 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:26:35,964 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:26:35,964 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:26:35,973 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:26:35,981 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:26:35,984 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:26:36,017 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:26:36,017 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:26:36,017 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:26:36,040 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:26:36,040 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:26:36,058 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:26:36,058 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:26:36,162 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:26:36,165 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:26:36,268 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_sample2.json
2025-05-30 23:38:38,481 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:38:38,482 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:38:38,482 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:38:38,504 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:38:38,504 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:38:38,504 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:38:38,504 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:38:39,705 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:38:39,768 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:38:39,769 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:38:39,776 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:38:39,776 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:38:39,777 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:38:39,783 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:38:39,783 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:38:39,783 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:38:39,783 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:38:40,018 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:38:40,065 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:38:40,065 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:38:40,069 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:38:40,073 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:40,073 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:40,073 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:40,073 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:40,082 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:40,090 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:40,093 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:40,125 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:40,125 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:40,125 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:40,148 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:40,148 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:40,165 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:40,165 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:40,268 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:40,271 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:40,314 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:38:40,315 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-08 13_49_37.615466_620226115c0616af26124166.json
2025-05-30 23:38:40,322 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:40,322 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:40,322 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:40,322 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:40,331 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:40,340 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:40,343 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:40,388 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:40,388 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:40,388 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:40,425 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:40,425 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:40,454 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:40,454 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:40,566 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:40,574 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:40,725 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-08 13_49_37.615466_620226115c0616af26124166.json
2025-05-30 23:38:40,726 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-27 12_15_31.237216_61f23f441d8fbe29f0c45c35.json
2025-05-30 23:38:40,731 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:40,731 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:40,731 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:40,732 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:40,747 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:40,760 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:40,763 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:40,845 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:40,845 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:40,845 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:40,908 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:40,909 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:40,959 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:40,959 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:41,070 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:41,078 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:41,235 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-27 12_15_31.237216_61f23f441d8fbe29f0c45c35.json
2025-05-30 23:38:41,236 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:38:41,239 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:41,239 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:41,239 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:41,239 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:41,245 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:41,250 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:41,254 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:41,273 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:41,273 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:41,273 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:41,287 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:41,287 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:41,297 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:41,297 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:41,404 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:41,409 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:41,486 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:38:41,487 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-18 11_33_00.536023_620f35f45c06161c56124180.json
2025-05-30 23:38:41,498 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:41,498 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:41,498 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:41,498 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:41,511 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:41,523 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:41,528 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:41,590 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:41,590 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:41,590 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:41,647 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:41,647 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:41,693 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:41,693 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:41,852 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:41,877 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:42,265 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-18 11_33_00.536023_620f35f45c06161c56124180.json
2025-05-30 23:38:42,267 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-12 11_32_09.999599_62074cd75c0616b4ab124169.json
2025-05-30 23:38:42,279 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:42,279 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:42,279 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:42,279 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:42,294 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:42,307 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:42,312 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:42,385 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:42,385 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:42,385 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:42,453 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:42,453 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:42,507 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:42,507 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:42,629 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:42,652 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:43,062 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-12 11_32_09.999599_62074cd75c0616b4ab124169.json
2025-05-30 23:38:43,064 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_32_50.719854_67ed65c92d5fe54f0102f5c8.json
2025-05-30 23:38:43,073 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:43,073 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:43,073 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:43,073 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:43,088 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:43,101 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:43,105 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:43,255 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:43,255 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:43,255 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:43,321 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:43,321 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:43,390 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:43,390 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:43,508 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:43,525 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:43,827 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_32_50.719854_67ed65c92d5fe54f0102f5c8.json
2025-05-30 23:38:43,828 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_48_40.731132_67ed65c92d5fe54f0102f580.json
2025-05-30 23:38:43,842 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:43,842 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:43,842 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:43,842 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:43,856 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:43,867 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:43,872 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:43,938 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:43,938 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:43,938 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:44,000 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:44,000 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:44,049 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:44,049 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:44,175 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:44,198 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:44,626 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_48_40.731132_67ed65c92d5fe54f0102f580.json
2025-05-30 23:38:44,629 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-27 12_18_33.904824_61f23f441d8fbe29f0c45c35.json
2025-05-30 23:38:44,646 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:44,646 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:44,646 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:44,646 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:44,679 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:44,708 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:44,713 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:44,914 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:44,914 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:44,914 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:45,096 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:45,096 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:45,242 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:45,242 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:45,372 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:45,399 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:45,879 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-27 12_18_33.904824_61f23f441d8fbe29f0c45c35.json
2025-05-30 23:38:45,880 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_26_01.514823_67ed65c92d5fe54f0102f5c1.json
2025-05-30 23:38:45,890 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:45,890 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:45,890 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:45,890 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:45,901 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:45,912 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:45,917 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:45,967 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:45,967 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:45,967 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:46,015 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:46,015 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:46,054 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:46,054 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:46,172 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:46,188 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:46,476 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_26_01.514823_67ed65c92d5fe54f0102f5c1.json
2025-05-30 23:38:46,477 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 12_00_45.497382_67ed65c92d5fe54f0102f58c.json
2025-05-30 23:38:46,487 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:46,487 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:46,487 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:46,487 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:46,497 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:46,506 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:46,510 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:46,549 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:46,549 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:46,549 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:46,584 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:46,584 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:46,610 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:46,611 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:46,729 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:46,747 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:47,056 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 12_00_45.497382_67ed65c92d5fe54f0102f58c.json
2025-05-30 23:38:47,058 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-18 11_34_12.915743_620f35f45c06161c56124180.json
2025-05-30 23:38:47,065 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:47,065 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:47,065 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:47,065 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:47,074 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:47,081 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:47,085 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:47,118 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:47,118 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:47,118 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:47,146 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:47,146 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:47,168 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:47,168 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:47,279 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:47,290 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:47,501 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-18 11_34_12.915743_620f35f45c06161c56124180.json
2025-05-30 23:38:47,502 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_28_43.597817_67ed65c92d5fe54f0102f5c1.json
2025-05-30 23:38:47,511 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:47,512 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:47,512 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:47,512 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:47,520 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:47,528 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:47,532 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:47,562 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:47,562 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:47,562 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:47,587 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:47,587 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:47,606 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:47,607 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:47,724 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:47,738 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:48,004 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_28_43.597817_67ed65c92d5fe54f0102f5c1.json
2025-05-30 23:38:48,005 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_49_47.024746_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:38:48,015 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:48,015 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:48,015 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:48,015 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:48,025 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:48,034 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:48,038 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:48,080 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:48,080 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:48,080 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:48,115 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:48,115 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:48,143 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:48,143 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:48,260 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:48,276 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:48,554 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-14 12_49_47.024746_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:38:48,555 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_33_46.179640_67ed65c92d5fe54f0102f5c8.json
2025-05-30 23:38:48,562 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:48,562 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:48,562 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:48,563 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:48,599 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:48,629 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:48,633 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:48,892 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:48,892 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:48,892 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:49,080 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:49,080 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:49,231 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:49,231 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:49,344 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:49,357 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:49,615 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_33_46.179640_67ed65c92d5fe54f0102f5c8.json
2025-05-30 23:38:49,616 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-09 11_27_02.924146_620357045c06165cc9124167.json
2025-05-30 23:38:49,633 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:49,633 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:49,633 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:49,633 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:49,644 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:49,653 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:49,659 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:49,702 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:49,702 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:49,703 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:49,741 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:49,741 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:49,772 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:49,772 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:49,904 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:49,939 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:50,616 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-09 11_27_02.924146_620357045c06165cc9124167.json
2025-05-30 23:38:50,618 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-12 12_15_27.794617_620752d45c0616549d12416a.json
2025-05-30 23:38:50,630 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:50,630 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:50,630 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:50,630 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:50,641 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:50,651 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:50,656 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:50,702 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:50,702 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:50,702 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:50,742 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:50,743 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:50,775 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:50,775 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:50,902 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:50,926 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:51,349 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-12 12_15_27.794617_620752d45c0616549d12416a.json
2025-05-30 23:38:51,350 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_59_19.766477_67ed65c92d5fe54f0102f58c.json
2025-05-30 23:38:51,359 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:51,360 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:51,360 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:51,360 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:51,366 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:51,373 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:51,378 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:51,398 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:51,398 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:51,398 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:51,414 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:51,415 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:51,427 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:51,427 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:51,546 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:51,561 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:51,843 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_59_19.766477_67ed65c92d5fe54f0102f58c.json
2025-05-30 23:38:51,845 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:38:51,855 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:51,855 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:51,855 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:51,855 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:51,863 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:51,870 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:51,875 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:51,906 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:51,906 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:51,906 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:51,931 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:51,931 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:51,951 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:51,951 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:52,116 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:52,134 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:52,445 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:38:52,446 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_53_08.094235_67ed65c92d5fe54f0102f580.json
2025-05-30 23:38:52,463 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:52,463 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:52,463 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:52,463 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:52,472 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:52,480 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:52,485 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:52,518 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:52,518 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:52,518 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:52,548 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:52,548 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:52,571 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:52,571 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:52,699 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:52,726 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:53,224 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_53_08.094235_67ed65c92d5fe54f0102f580.json
2025-05-30 23:38:53,226 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-12 12_17_39.554440_620752d45c0616549d12416a.json
2025-05-30 23:38:53,240 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:53,241 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:53,241 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:53,241 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:53,255 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:53,267 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:53,272 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:53,344 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:53,344 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:53,344 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:53,412 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:53,412 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:53,465 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:53,465 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:53,591 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:53,614 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:54,037 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-12 12_17_39.554440_620752d45c0616549d12416a.json
2025-05-30 23:38:54,038 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:38:54,047 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:54,047 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:54,047 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:54,047 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:54,060 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:54,070 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:54,074 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:54,132 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:54,132 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:54,132 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:54,179 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:54,179 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:54,216 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:54,216 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:54,332 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:54,344 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:54,562 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:38:54,563 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-18 11_35_08.376228_620f35f45c06161c56124180.json
2025-05-30 23:38:54,567 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:54,567 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:54,567 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:54,568 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:54,574 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:54,579 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:54,583 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:54,603 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:54,603 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:54,603 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:54,617 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:54,617 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:54,628 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:54,628 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:54,736 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:54,744 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:54,862 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-18 11_35_08.376228_620f35f45c06161c56124180.json
2025-05-30 23:38:54,863 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_27_16.129215_67ed65c92d5fe54f0102f5c1.json
2025-05-30 23:38:54,871 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:54,871 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:54,871 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:54,871 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:54,878 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:54,884 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:54,889 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:54,912 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:54,912 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:54,912 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:54,930 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:54,930 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:54,944 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:54,944 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:55,062 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:55,076 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:55,335 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_27_16.129215_67ed65c92d5fe54f0102f5c1.json
2025-05-30 23:38:55,336 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:38:55,344 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:55,344 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:55,344 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:55,344 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:55,353 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:55,361 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:55,364 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:55,402 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:55,402 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:55,402 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:55,432 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:55,432 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:55,456 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:55,456 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:55,570 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:55,585 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:55,804 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:38:55,805 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-08 13_47_30.949284_620226115c0616af26124166.json
2025-05-30 23:38:55,811 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:55,811 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:55,811 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:55,811 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:55,822 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:55,830 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:55,835 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:55,882 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:55,883 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:55,883 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:55,920 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:55,920 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:55,951 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:55,951 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:56,064 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:56,075 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:56,258 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-08 13_47_30.949284_620226115c0616af26124166.json
2025-05-30 23:38:56,259 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:38:56,268 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:56,268 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:56,268 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:56,268 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:56,279 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:56,288 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:56,292 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:56,340 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:56,340 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:56,340 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:56,381 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:56,381 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:56,414 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:56,414 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:56,533 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:56,549 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:56,830 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:38:56,831 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-27 12_16_59.682831_61f23f441d8fbe29f0c45c35.json
2025-05-30 23:38:56,838 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:56,839 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:56,839 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:56,839 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:56,860 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:56,879 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:56,883 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:57,010 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:57,010 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:57,010 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:57,114 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:57,115 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:57,197 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:57,198 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:57,314 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:57,327 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:57,574 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-27 12_16_59.682831_61f23f441d8fbe29f0c45c35.json
2025-05-30 23:38:57,575 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_35_10.223199_67ed65c92d5fe54f0102f5c8.json
2025-05-30 23:38:57,583 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:57,583 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:57,583 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:57,584 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:57,602 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:57,617 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:57,622 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:57,715 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:57,715 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:57,715 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:57,799 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:57,799 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:57,866 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:57,867 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:57,983 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:57,997 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:58,304 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_35_10.223199_67ed65c92d5fe54f0102f5c8.json
2025-05-30 23:38:58,305 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_50_36.233107_67ed65c92d5fe54f0102f580.json
2025-05-30 23:38:58,317 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:58,317 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:58,317 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:58,317 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:58,334 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:58,349 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:58,354 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:58,439 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:58,439 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:58,439 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:58,516 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:58,516 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:58,577 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:58,577 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:58,699 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:58,720 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:38:59,100 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_50_36.233107_67ed65c92d5fe54f0102f580.json
2025-05-30 23:38:59,102 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-09 11_33_39.854538_620357045c06165cc9124167.json
2025-05-30 23:38:59,122 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:38:59,122 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:38:59,122 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:38:59,122 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:38:59,137 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:38:59,150 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:38:59,157 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:38:59,229 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:38:59,230 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:38:59,230 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:38:59,302 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:38:59,302 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:38:59,358 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:38:59,358 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:38:59,500 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:38:59,536 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:00,325 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-09 11_33_39.854538_620357045c06165cc9124167.json
2025-05-30 23:39:00,327 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:39:00,334 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:00,334 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:00,334 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:00,335 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:00,340 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:00,346 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:00,350 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:00,366 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:00,366 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:00,366 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:00,379 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:00,379 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:00,388 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:00,388 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:00,505 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:00,517 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:00,729 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:39:00,730 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:39:00,733 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:00,733 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:00,733 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:00,733 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:00,738 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:00,742 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:00,745 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:00,758 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:00,758 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:00,758 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:00,767 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:00,767 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:00,773 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:00,773 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:00,879 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:00,882 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:00,948 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:39:00,949 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:39:00,950 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:00,950 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:00,950 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:00,950 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:00,954 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:00,958 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:00,961 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:00,965 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:00,965 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:00,965 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:00,968 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:00,968 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:00,970 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:00,970 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:01,058 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:01,059 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:01,061 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:39:01,061 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-12 12_19_02.354145_620752d45c0616549d12416a.json
2025-05-30 23:39:01,069 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:01,069 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:01,069 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:01,069 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:01,078 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:01,087 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:01,091 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:01,129 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:01,129 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:01,129 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:01,161 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:01,161 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:01,187 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:01,187 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:01,303 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:01,318 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:01,564 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-12 12_19_02.354145_620752d45c0616549d12416a.json
2025-05-30 23:39:01,565 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_40_30.552102_67ed65c92d5fe54f0102f579.json
2025-05-30 23:39:01,577 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:01,577 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:01,577 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:01,577 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:01,587 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:01,595 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:01,600 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:01,631 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:01,631 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:01,631 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:01,658 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:01,658 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:01,678 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:01,678 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:01,803 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:01,825 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:02,212 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_40_30.552102_67ed65c92d5fe54f0102f579.json
2025-05-30 23:39:02,214 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-09 11_30_21.913888_620357045c06165cc9124167.json
2025-05-30 23:39:02,235 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:02,236 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:02,236 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:02,236 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:02,250 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:02,263 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:02,270 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:02,339 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:02,339 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:02,339 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:02,408 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:02,408 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:02,462 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:02,462 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:02,602 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:02,642 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:03,337 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-09 11_30_21.913888_620357045c06165cc9124167.json
2025-05-30 23:39:03,339 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-12 11_36_14.208262_62074cd75c0616b4ab124169.json
2025-05-30 23:39:03,352 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:03,352 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:03,352 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:03,352 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:03,370 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:03,385 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:03,390 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:03,491 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:03,491 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:03,491 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:03,583 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:03,583 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:03,659 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:03,659 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:03,782 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:03,806 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:04,215 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-12 11_36_14.208262_62074cd75c0616b4ab124169.json
2025-05-30 23:39:04,217 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-08 13_45_25.328039_620226115c0616af26124166.json
2025-05-30 23:39:04,225 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:04,225 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:04,226 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:04,226 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:04,241 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:04,253 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:04,257 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:04,336 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:04,336 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:04,336 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:04,404 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:04,404 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:04,459 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:04,459 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:04,584 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:04,599 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:04,851 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-08 13_45_25.328039_620226115c0616af26124166.json
2025-05-30 23:39:04,852 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_41_53.821624_67ed65c92d5fe54f0102f579.json
2025-05-30 23:39:04,862 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:04,862 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:04,862 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:04,862 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:04,871 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:04,880 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:04,885 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:04,925 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:04,925 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:04,925 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:04,959 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:04,959 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:04,985 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:04,985 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:05,108 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:05,125 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:05,440 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_41_53.821624_67ed65c92d5fe54f0102f579.json
2025-05-30 23:39:05,441 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 12_02_02.630081_67ed65c92d5fe54f0102f58c.json
2025-05-30 23:39:05,450 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:05,450 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:05,450 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:05,450 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:05,458 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:05,465 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:05,469 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:05,497 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:05,497 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:05,497 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:05,522 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:05,522 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:05,540 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:05,540 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:05,655 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:05,668 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:05,893 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 12_02_02.630081_67ed65c92d5fe54f0102f58c.json
2025-05-30 23:39:05,894 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:39:05,899 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:05,899 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:05,900 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:05,900 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:05,905 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:05,910 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:05,913 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:05,928 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:05,928 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:05,928 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:05,939 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:05,939 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:05,947 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:05,947 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:06,056 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:06,062 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:06,152 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:39:06,153 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:39:06,167 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:06,167 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:06,167 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:06,168 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:06,180 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:06,191 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:06,196 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:06,253 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:06,253 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:06,253 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:06,308 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:06,308 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:06,352 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:06,352 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:06,484 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:06,511 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:07,003 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:39:07,005 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2025-04-03 11_42_51.162206_67ed65c92d5fe54f0102f579.json
2025-05-30 23:39:07,014 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:07,014 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:07,014 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:07,014 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:07,022 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:07,028 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:07,031 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:07,053 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:07,053 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:07,053 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:07,070 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:07,070 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:07,082 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:07,082 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:07,198 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:07,210 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:07,424 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2025-04-03 11_42_51.162206_67ed65c92d5fe54f0102f579.json
2025-05-30 23:39:07,426 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:39:07,431 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:07,431 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:07,432 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:07,432 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:07,439 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:07,445 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:07,449 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:07,474 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:07,475 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:07,475 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:07,493 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:07,493 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:07,506 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:07,506 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:07,620 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:07,631 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:07,817 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:39:07,818 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_53_50.743681_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:39:07,821 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:07,821 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:07,821 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:07,821 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:07,828 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:07,835 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:07,838 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:07,864 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:07,864 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:07,864 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:07,884 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:07,884 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:07,900 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:07,900 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:08,000 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:08,003 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:08,043 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-01-14 12_53_50.743681_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:39:08,043 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-02-12 11_34_15.518256_62074cd75c0616b4ab124169.json
2025-05-30 23:39:08,056 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:39:08,056 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:39:08,056 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:39:08,056 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:39:08,077 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:39:08,095 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:39:08,100 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:39:08,223 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:39:08,224 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:39:08,224 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:39:08,338 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:39:08,338 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:39:08,430 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:39:08,430 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:39:08,555 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:39:08,581 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:39:09,024 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data_complete/enhanced_Coloring_2022-02-12 11_34_15.518256_62074cd75c0616b4ab124169.json
2025-05-30 23:39:17,679 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 47/47 files processed successfully
