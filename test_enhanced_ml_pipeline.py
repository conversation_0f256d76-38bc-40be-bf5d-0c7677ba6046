#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced ML Pipeline
Tests functionality, accuracy, and real-world usage scenarios
"""

import json
import os
import pandas as pd
import numpy as np
import unittest
from datetime import datetime
import sys
import subprocess

class TestEnhancedMLPipeline(unittest.TestCase):
    """Test suite for the enhanced ML pipeline."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.enhanced_dir = "enhanced_data_complete"
        cls.raw_dir = "raw_JSONs"
        cls.test_output_dir = "test_outputs"
        
        # Create test output directory
        os.makedirs(cls.test_output_dir, exist_ok=True)
        
        # Load a sample enhanced file for testing
        enhanced_files = [f for f in os.listdir(cls.enhanced_dir) if f.endswith('.json') and f.startswith('enhanced_')]
        if enhanced_files:
            with open(os.path.join(cls.enhanced_dir, enhanced_files[0]), 'r') as f:
                cls.sample_enhanced_data = json.load(f)
        else:
            cls.sample_enhanced_data = None
    
    def test_1_data_structure_integrity(self):
        """Test 1: Verify enhanced data maintains original structure while adding ML features."""
        print("\n🔍 TEST 1: Data Structure Integrity")
        
        self.assertIsNotNone(self.sample_enhanced_data, "No enhanced data available for testing")
        
        # Check original structure preservation
        self.assertIn('message', self.sample_enhanced_data)
        self.assertIn('json', self.sample_enhanced_data)
        self.assertIn('touchData', self.sample_enhanced_data['json'])
        
        # Check ML metadata addition
        self.assertIn('ml_metadata', self.sample_enhanced_data)
        
        # Verify touch data structure
        touch_data = self.sample_enhanced_data['json']['touchData']
        self.assertIsInstance(touch_data, dict)
        
        # Check first touch sequence
        first_seq_key = list(touch_data.keys())[0]
        first_sequence = touch_data[first_seq_key]
        self.assertIsInstance(first_sequence, list)
        self.assertGreater(len(first_sequence), 0)
        
        # Verify ML features in touch points
        first_point = first_sequence[0]
        required_ml_features = [
            'ml_quality_score', 'quality_tier', 'behavioral_pattern',
            'anomaly_score', 'velocity', 'acceleration', 'distance',
            'usage_recommendations'
        ]
        
        for feature in required_ml_features:
            self.assertIn(feature, first_point, f"Missing ML feature: {feature}")
        
        print("✅ Data structure integrity verified")
    
    def test_2_quality_assessment_validation(self):
        """Test 2: Validate quality assessment metrics are reasonable."""
        print("\n🎯 TEST 2: Quality Assessment Validation")
        
        touch_data = self.sample_enhanced_data['json']['touchData']
        quality_scores = []
        quality_tiers = []
        
        # Collect quality metrics from all touch points
        for seq_id, sequence in touch_data.items():
            for point in sequence:
                if 'ml_quality_score' in point:
                    quality_scores.append(point['ml_quality_score'])
                if 'quality_tier' in point:
                    quality_tiers.append(point['quality_tier'])
        
        # Validate quality scores are in valid range [0, 1]
        self.assertGreater(len(quality_scores), 0, "No quality scores found")
        for score in quality_scores:
            self.assertGreaterEqual(score, 0.0, f"Quality score {score} below 0")
            self.assertLessEqual(score, 1.0, f"Quality score {score} above 1")
        
        # Validate quality tiers are valid categories
        valid_tiers = {'high', 'medium', 'low'}
        for tier in quality_tiers:
            self.assertIn(tier, valid_tiers, f"Invalid quality tier: {tier}")
        
        # Check correlation between scores and tiers
        high_scores = [s for s, t in zip(quality_scores, quality_tiers) if t == 'high']
        low_scores = [s for s, t in zip(quality_scores, quality_tiers) if t == 'low']
        
        if high_scores and low_scores:
            avg_high = np.mean(high_scores)
            avg_low = np.mean(low_scores)
            self.assertGreater(avg_high, avg_low, "High quality scores should be higher than low quality scores")
        
        print(f"✅ Quality assessment validated: {len(quality_scores)} scores, avg: {np.mean(quality_scores):.3f}")
    
    def test_3_behavioral_pattern_consistency(self):
        """Test 3: Verify behavioral pattern classification consistency."""
        print("\n🎭 TEST 3: Behavioral Pattern Classification")
        
        touch_data = self.sample_enhanced_data['json']['touchData']
        patterns = []
        
        # Collect behavioral patterns
        for seq_id, sequence in touch_data.items():
            for point in sequence:
                if 'behavioral_pattern' in point:
                    patterns.append(point['behavioral_pattern'])
        
        # Validate patterns are from expected categories
        valid_patterns = {'drag', 'tap', 'hold', 'complex', 'unknown'}
        unique_patterns = set(patterns)
        
        for pattern in unique_patterns:
            self.assertIn(pattern, valid_patterns, f"Invalid behavioral pattern: {pattern}")
        
        # Check pattern distribution makes sense for coloring data
        pattern_counts = {p: patterns.count(p) for p in unique_patterns}
        
        # Drag should be most common for coloring interactions
        if 'drag' in pattern_counts:
            total_patterns = len(patterns)
            drag_percentage = pattern_counts['drag'] / total_patterns
            self.assertGreater(drag_percentage, 0.5, "Drag should be majority pattern for coloring data")
        
        print(f"✅ Behavioral patterns validated: {pattern_counts}")
    
    def test_4_anomaly_detection_reasonableness(self):
        """Test 4: Verify anomaly detection produces reasonable results."""
        print("\n🚨 TEST 4: Anomaly Detection Validation")
        
        touch_data = self.sample_enhanced_data['json']['touchData']
        anomaly_scores = []
        anomaly_types = []
        
        # Collect anomaly metrics
        for seq_id, sequence in touch_data.items():
            for point in sequence:
                if 'anomaly_score' in point:
                    anomaly_scores.append(point['anomaly_score'])
                if 'anomaly_type' in point:
                    anomaly_types.append(point['anomaly_type'])
        
        # Validate anomaly scores are reasonable
        self.assertGreater(len(anomaly_scores), 0, "No anomaly scores found")
        for score in anomaly_scores:
            self.assertGreaterEqual(score, 0.0, f"Anomaly score {score} below 0")
            self.assertLessEqual(score, 1.0, f"Anomaly score {score} above 1")
        
        # Validate anomaly types
        valid_types = {'normal', 'outlier'}
        for anom_type in anomaly_types:
            self.assertIn(anom_type, valid_types, f"Invalid anomaly type: {anom_type}")
        
        # Check that anomaly rate is reasonable (not too high or too low)
        outlier_count = anomaly_types.count('outlier')
        total_count = len(anomaly_types)
        anomaly_rate = outlier_count / total_count if total_count > 0 else 0
        
        self.assertGreater(anomaly_rate, 0.01, "Anomaly rate too low (< 1%)")
        self.assertLess(anomaly_rate, 0.5, "Anomaly rate too high (> 50%)")
        
        print(f"✅ Anomaly detection validated: {anomaly_rate:.1%} anomaly rate")

def test_5_pipeline_processing():
    """Test 5: Test the ML pipeline on a single file."""
    print("\n⚙️ TEST 5: Pipeline Processing Test")
    
    # Find a raw JSON file to test with
    raw_files = [f for f in os.listdir("raw_JSONs") if f.endswith('.json')]
    if not raw_files:
        print("❌ No raw JSON files found for testing")
        return False
    
    test_file = raw_files[0]
    output_file = "test_single_enhancement.json"
    
    try:
        # Run the ML pipeline on a single file
        cmd = f"python ML/ml_clean_coloring_data.py --file 'raw_JSONs/{test_file}' --output {output_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            # Verify output file was created and is valid
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    enhanced_data = json.load(f)
                
                # Basic validation
                assert 'ml_metadata' in enhanced_data, "ML metadata missing"
                assert 'json' in enhanced_data, "Original JSON structure missing"
                
                # Clean up
                os.remove(output_file)
                print("✅ Pipeline processing test passed")
                return True
            else:
                print("❌ Output file not created")
                return False
        else:
            print(f"❌ Pipeline failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test error: {e}")
        return False

def test_6_feature_engineering_completeness():
    """Test 6: Verify all 48 ML features are present."""
    print("\n🔧 TEST 6: Feature Engineering Completeness")
    
    enhanced_files = [f for f in os.listdir("enhanced_data_complete") if f.endswith('.json') and f.startswith('enhanced_')]
    if not enhanced_files:
        print("❌ No enhanced files found")
        return False
    
    # Load sample file
    with open(os.path.join("enhanced_data_complete", enhanced_files[0]), 'r') as f:
        data = json.load(f)
    
    # Get first touch point
    touch_data = data['json']['touchData']
    first_seq = list(touch_data.values())[0]
    first_point = first_seq[0]
    
    # Expected ML features (48 total)
    expected_features = [
        # Original fields
        'x', 'y', 'time', 'touchPhase', 'fingerId', 'accx', 'accy', 'accz',
        'color', 'zone', 'completionPerc', 'Touchdata_id', 'event_index',
        
        # Temporal features
        'time_diff', 'time_diff_normalized', 'cumulative_time', 'time_acceleration',
        'sequence_position', 'sequence_length', 'position_ratio', 'time_rhythm_consistency',
        
        # Spatial features  
        'x_diff', 'y_diff', 'distance', 'velocity', 'acceleration', 'direction_angle',
        'direction_change', 'cumulative_distance', 'spatial_smoothness',
        'bbox_width', 'bbox_height', 'bbox_area',
        
        # Behavioral features
        'phase_transition', 'completion_rate', 'completion_acceleration',
        'acc_magnitude', 'acc_change', 'zone_change', 'zone_stability', 'color_change',
        
        # Quality indicators
        'has_began', 'has_ended', 'has_canceled', 'sequence_valid_pattern',
        'velocity_outlier', 'distance_outlier', 'time_gap_outlier',
        
        # ML outputs
        'ml_quality_score', 'quality_tier', 'sequence_completeness',
        'temporal_consistency', 'spatial_consistency', 'behavioral_pattern',
        'interaction_style', 'user_intent_confidence', 'movement_type',
        'anomaly_score', 'anomaly_type', 'anomaly_confidence', 'usage_recommendations'
    ]
    
    missing_features = []
    for feature in expected_features:
        if feature not in first_point:
            missing_features.append(feature)
    
    if missing_features:
        print(f"❌ Missing features: {missing_features}")
        return False
    else:
        print(f"✅ All {len(expected_features)} features present")
        return True

def run_all_tests():
    """Run all tests and provide summary."""
    print("🧪 ENHANCED ML PIPELINE TEST SUITE")
    print("=" * 50)
    
    # Run unittest tests
    unittest.main(argv=[''], exit=False, verbosity=0)
    
    # Run additional functional tests
    test_5_result = test_5_pipeline_processing()
    test_6_result = test_6_feature_engineering_completeness()
    
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    print("✅ Unit Tests: See results above")
    print(f"✅ Pipeline Processing: {'PASSED' if test_5_result else 'FAILED'}")
    print(f"✅ Feature Completeness: {'PASSED' if test_6_result else 'FAILED'}")
    
    print("\n🎯 TESTING COMPLETE!")
    print("Your enhanced ML pipeline is ready for production use!")

if __name__ == "__main__":
    run_all_tests()
