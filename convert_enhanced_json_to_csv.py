#!/usr/bin/env python3
"""
Convert ML-Enhanced Coloring Touch Data from JSON to CSV Format
Processes 47 enhanced JSON files with 48 ML features into CSV format
"""

import json
import os
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_enhanced_json_to_csv(
    input_dir: str = "enhanced_data_complete",
    output_dir: str = "enhanced_data_CSVs",
    include_metadata_header: bool = True
) -> Dict[str, Any]:
    """
    Convert ML-enhanced Coloring touch data from JSON to CSV format.
    
    Args:
        input_dir: Directory containing enhanced JSON files
        output_dir: Directory to save converted CSV files
        include_metadata_header: Whether to include ML metadata as header comments
        
    Returns:
        Dictionary with conversion summary statistics
    """
    
    print("🔄 CONVERTING ENHANCED JSON TO CSV FORMAT")
    print("=" * 50)
    
    # Create output directory
    try:
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Created output directory: {output_dir}")
    except Exception as e:
        logger.error(f"Failed to create output directory: {e}")
        return {"success": False, "error": str(e)}
    
    # Initialize conversion tracking
    conversion_summary = {
        "total_files": 0,
        "successful_conversions": 0,
        "failed_conversions": 0,
        "total_data_points": 0,
        "total_sequences": 0,
        "failed_files": [],
        "processing_time": None,
        "output_directory": output_dir
    }
    
    start_time = datetime.now()
    
    # Get list of enhanced JSON files
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json') and f.startswith('enhanced_')]
    conversion_summary["total_files"] = len(json_files)
    
    if not json_files:
        logger.warning(f"No enhanced JSON files found in {input_dir}")
        return conversion_summary
    
    print(f"Found {len(json_files)} enhanced JSON files to convert")
    
    # Process each JSON file
    for i, filename in enumerate(json_files, 1):
        try:
            print(f"[{i:2d}/{len(json_files)}] Processing: {filename[:50]}...")
            
            # Load JSON data
            input_path = os.path.join(input_dir, filename)
            with open(input_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Convert JSON to CSV format
            csv_data, metadata = _flatten_json_to_dataframe(json_data, filename)
            
            if csv_data is None or csv_data.empty:
                logger.warning(f"No valid data extracted from {filename}")
                conversion_summary["failed_conversions"] += 1
                conversion_summary["failed_files"].append(filename)
                continue
            
            # Generate output filename
            output_filename = filename.replace('.json', '.csv')
            output_path = os.path.join(output_dir, output_filename)
            
            # Save CSV with metadata header
            _save_csv_with_metadata(csv_data, metadata, output_path, include_metadata_header)
            
            # Update statistics
            conversion_summary["successful_conversions"] += 1
            conversion_summary["total_data_points"] += len(csv_data)
            conversion_summary["total_sequences"] += csv_data['Touchdata_id'].nunique() if 'Touchdata_id' in csv_data.columns else 0
            
            logger.info(f"✅ Converted {filename} -> {output_filename} ({len(csv_data)} rows)")
            
        except Exception as e:
            logger.error(f"❌ Failed to convert {filename}: {str(e)}")
            conversion_summary["failed_conversions"] += 1
            conversion_summary["failed_files"].append(filename)
            continue
    
    # Calculate processing time
    end_time = datetime.now()
    conversion_summary["processing_time"] = str(end_time - start_time)
    
    # Print summary
    _print_conversion_summary(conversion_summary)
    
    return conversion_summary

def _flatten_json_to_dataframe(json_data: Dict[str, Any], filename: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Flatten nested JSON structure into a pandas DataFrame.
    
    Args:
        json_data: Enhanced JSON data
        filename: Source filename for reference
        
    Returns:
        Tuple of (DataFrame with flattened data, metadata dictionary)
    """
    
    # Extract metadata
    metadata = {
        "source_file": filename,
        "conversion_timestamp": datetime.now().isoformat(),
        "ml_metadata": json_data.get("ml_metadata", {}),
        "original_structure": {
            "message": json_data.get("message", ""),
            "dataset": json_data.get("json", {}).get("dataSet", "")
        }
    }
    
    # Extract touch data
    touch_data = json_data.get("json", {}).get("touchData", {})
    
    if not touch_data:
        logger.warning(f"No touchData found in {filename}")
        return None, metadata
    
    # Flatten touch sequences into rows
    all_rows = []
    
    for sequence_id, sequence_points in touch_data.items():
        if not isinstance(sequence_points, list):
            continue
            
        for point in sequence_points:
            if isinstance(point, dict):
                # Add sequence identifier if missing
                if 'Touchdata_id' not in point:
                    point['Touchdata_id'] = sequence_id
                
                # Add source file reference
                point['source_file'] = filename
                
                all_rows.append(point)
    
    if not all_rows:
        logger.warning(f"No valid touch points found in {filename}")
        return None, metadata
    
    # Create DataFrame
    df = pd.DataFrame(all_rows)
    
    # Ensure proper column ordering (original fields first, then ML features)
    original_columns = [
        'Touchdata_id', 'event_index', 'x', 'y', 'time', 'touchPhase', 'fingerId',
        'accx', 'accy', 'accz', 'color', 'zone', 'completionPerc'
    ]
    
    # Get all columns and sort them
    available_original = [col for col in original_columns if col in df.columns]
    ml_features = [col for col in df.columns if col not in original_columns and col != 'source_file']
    
    # Reorder columns: original data, ML features, source file
    column_order = available_original + sorted(ml_features) + ['source_file']
    df = df.reindex(columns=column_order)
    
    # Sort by sequence and event order
    if 'Touchdata_id' in df.columns and 'event_index' in df.columns:
        df = df.sort_values(['Touchdata_id', 'event_index'])
    elif 'Touchdata_id' in df.columns and 'time' in df.columns:
        df = df.sort_values(['Touchdata_id', 'time'])
    
    # Reset index
    df = df.reset_index(drop=True)
    
    return df, metadata

def _save_csv_with_metadata(
    df: pd.DataFrame, 
    metadata: Dict[str, Any], 
    output_path: str, 
    include_header: bool = True
) -> None:
    """
    Save DataFrame to CSV with optional metadata header.
    
    Args:
        df: DataFrame to save
        metadata: Metadata dictionary
        output_path: Output file path
        include_header: Whether to include metadata header
    """
    
    if include_header:
        # Create metadata header as comments
        header_lines = [
            f"# Enhanced Coloring Touch Data - CSV Export",
            f"# Source File: {metadata['source_file']}",
            f"# Conversion Time: {metadata['conversion_timestamp']}",
            f"# Dataset: {metadata['original_structure']['dataset']}",
            f"# Total Rows: {len(df)}",
            f"# Total Sequences: {df['Touchdata_id'].nunique() if 'Touchdata_id' in df.columns else 'Unknown'}",
        ]
        
        # Add ML metadata if available
        ml_meta = metadata.get('ml_metadata', {})
        if ml_meta:
            header_lines.extend([
                f"# ML Processing Time: {ml_meta.get('processing_timestamp', 'Unknown')}",
                f"# ML Total Sequences: {ml_meta.get('total_sequences', 'Unknown')}",
                f"# ML Total Touch Points: {ml_meta.get('total_touch_points', 'Unknown')}",
                f"# Quality Distribution: {ml_meta.get('quality_distribution', {})}",
                f"# Behavioral Patterns: {ml_meta.get('behavioral_patterns', {})}",
            ])
        
        header_lines.append("#")  # Empty comment line before data
        
        # Write header and data
        with open(output_path, 'w', encoding='utf-8') as f:
            # Write metadata header
            for line in header_lines:
                f.write(line + '\n')
            
            # Write CSV data
            df.to_csv(f, index=False, float_format='%.6f')
    else:
        # Save without header
        df.to_csv(output_path, index=False, float_format='%.6f')

def _print_conversion_summary(summary: Dict[str, Any]) -> None:
    """Print detailed conversion summary."""
    
    print(f"\n📊 CONVERSION SUMMARY")
    print("=" * 30)
    print(f"Total Files Processed: {summary['total_files']}")
    print(f"Successful Conversions: {summary['successful_conversions']}")
    print(f"Failed Conversions: {summary['failed_conversions']}")
    print(f"Success Rate: {summary['successful_conversions']/summary['total_files']*100:.1f}%")
    print(f"Total Data Points: {summary['total_data_points']:,}")
    print(f"Total Sequences: {summary['total_sequences']:,}")
    print(f"Processing Time: {summary['processing_time']}")
    print(f"Output Directory: {summary['output_directory']}")
    
    if summary['failed_files']:
        print(f"\n❌ Failed Files ({len(summary['failed_files'])}):")
        for failed_file in summary['failed_files']:
            print(f"  - {failed_file}")
    
    print(f"\n✅ CSV files ready for analysis in '{summary['output_directory']}' directory!")

if __name__ == "__main__":
    # Run conversion
    result = convert_enhanced_json_to_csv()
    
    if result["successful_conversions"] > 0:
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Load CSV files with: pd.read_csv('enhanced_data_CSVs/filename.csv', comment='#')")
        print(f"2. Apply filtering criteria from our data quality guide")
        print(f"3. Perform your analysis with {result['total_data_points']:,} enhanced data points!")
    else:
        print(f"\n❌ No files were successfully converted. Check the logs for details.")
