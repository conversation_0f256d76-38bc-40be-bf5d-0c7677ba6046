2025-05-30 22:53:59,343 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 22:53:59,343 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 22:53:59,343 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 22:53:59,365 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 22:53:59,365 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 22:53:59,365 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 22:53:59,365 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 22:54:00,632 - __main__ - ERROR - Failed to initialize ML cleaner: string indices must be integers, not 'str'
2025-05-30 22:55:25,491 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 22:55:25,492 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 22:55:25,492 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 22:55:25,498 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 22:55:25,498 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 22:55:25,498 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 22:55:25,498 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 22:55:26,645 - __main__ - ERROR - Failed to initialize ML cleaner: string indices must be integers, not 'str'
2025-05-30 23:03:30,366 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:03:30,366 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:03:30,367 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:03:30,387 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:03:30,388 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:03:30,388 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:03:30,388 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:03:31,545 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:03:31,613 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:03:31,613 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:03:31,620 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:03:31,620 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:03:31,621 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:03:31,628 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:03:31,628 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:03:31,628 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:03:31,628 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:03:31,866 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:03:31,913 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:03:31,913 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:03:31,918 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:31,921 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:31,921 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:31,921 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:31,921 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:31,930 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:31,937 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:31,941 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:31,956 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:31,956 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:03:31,959 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:31,959 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:31,960 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:31,960 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:31,965 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:31,971 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:31,974 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:31,984 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:31,984 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:03:31,993 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:31,993 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:31,993 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:31,993 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,001 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,008 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,012 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,027 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,028 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:32,034 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,034 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,034 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,034 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,046 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,057 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,060 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,087 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,088 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:03:32,095 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,095 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,095 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,095 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,103 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,111 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,115 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,133 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,133 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:03:32,141 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,141 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,141 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,141 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,151 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,160 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,164 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,187 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,187 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:32,194 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,194 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,194 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,194 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,200 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,205 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,209 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,219 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,219 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:03:32,221 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,221 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,221 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,222 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,226 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,231 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,234 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,240 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,241 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:03:32,241 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,242 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,242 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,242 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,245 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,249 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,252 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,254 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json: Cannot set a DataFrame with multiple columns to the single column sequence_valid_pattern
2025-05-30 23:03:32,254 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:03:32,258 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,258 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,258 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,258 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,263 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,268 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,271 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,279 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,279 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:03:32,292 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,292 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,292 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,293 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,305 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,316 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,321 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,349 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,349 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:03:32,356 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:03:32,356 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:03:32,356 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:03:32,356 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:03:32,362 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:03:32,368 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:03:32,372 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:03:32,384 - ML.ml_cleaning_pipeline - ERROR - Error processing raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json: 'Series' object has no attribute 'mad'
2025-05-30 23:03:32,385 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 0/12 files processed successfully
2025-05-30 23:06:01,335 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:06:01,335 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:06:01,335 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:06:01,341 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:06:01,341 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:06:01,341 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:06:01,341 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:06:02,485 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:06:02,536 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:06:02,537 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:06:02,541 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:06:02,541 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:06:02,541 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:06:02,548 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:06:02,548 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:06:02,548 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:06:02,548 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:06:02,782 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:06:02,828 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:06:02,828 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:06:02,834 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:02,836 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:02,837 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:02,837 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:02,837 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:02,846 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:02,853 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:02,856 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:02,888 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:02,888 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:02,888 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:02,912 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:02,912 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:02,930 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:02,930 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,035 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,038 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,039 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,043 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:03,044 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:03,047 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,047 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,047 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,047 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,053 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,058 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,061 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,081 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,081 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,081 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,095 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,095 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:03,106 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:03,106 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,213 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,218 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,218 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,226 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:03,227 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:03,236 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,236 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,236 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,236 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,244 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,293 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,298 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,336 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,336 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,337 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,360 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,360 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:03,379 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:03,379 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,503 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,522 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,524 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,555 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:03,556 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:03,563 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,563 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,563 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,563 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,575 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,585 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,589 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,646 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,647 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,647 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,694 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,694 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:03,731 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:03,731 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:03,847 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:03,859 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:03,860 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:03,882 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:03,883 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:03,889 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:03,889 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:03,889 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:03,889 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:03,898 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:03,906 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:03,909 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:03,946 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:03,946 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:03,946 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:03,976 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:03,976 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,000 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,000 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,115 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,127 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,129 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,151 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:04,152 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:04,160 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,160 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,160 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,160 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,171 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,180 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,184 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,232 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,232 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,232 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,273 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,273 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,307 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,307 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,426 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,441 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,443 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,470 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:04,471 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:04,477 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,477 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,477 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,478 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,484 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,489 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,493 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,509 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,509 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,509 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,523 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,523 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,533 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,533 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,650 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,662 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,663 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,685 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:04,686 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:04,689 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,689 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,689 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,689 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,693 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,698 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,702 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,714 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,714 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,714 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,723 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,723 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,730 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,730 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:04,836 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:04,839 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:04,840 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:04,848 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:04,848 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:04,849 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:04,849 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:04,849 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:04,849 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:04,853 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:04,857 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:04,860 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:04,865 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:04,865 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:04,865 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:04,868 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:04,868 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:04,869 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:04,870 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,007 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,008 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,008 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,009 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:06:05,009 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:05,012 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:05,012 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:05,012 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:05,012 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:05,018 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:05,023 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:05,026 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:05,040 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:05,041 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:05,041 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:05,052 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:05,052 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:05,059 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:05,060 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,169 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,174 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,175 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,185 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:06:05,186 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:05,200 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:05,200 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:05,200 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:05,200 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:05,214 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:05,225 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:05,231 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:05,289 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:05,289 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:05,289 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:05,346 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:05,346 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:05,391 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:05,392 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,526 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,554 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,556 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,604 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:06:05,606 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:05,612 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:06:05,612 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:06:05,612 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:06:05,612 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:06:05,619 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:06:05,624 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:06:05,629 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:06:05,652 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:06:05,652 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:06:05,652 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:06:05,671 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:06:05,671 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:06:05,685 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:06:05,685 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:06:05,805 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:06:05,816 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:06:05,817 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:06:05,837 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:06:06,083 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:07:21,785 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:07:21,785 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:07:21,785 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:07:21,791 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:07:21,792 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:07:21,792 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:07:21,792 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:07:23,004 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:07:23,058 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:07:23,058 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:07:23,063 - __main__ - INFO - Processing single file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:07:23,063 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:07:23,066 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:07:23,066 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:07:23,067 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:07:23,067 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:07:23,076 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:07:23,085 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:07:23,088 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:07:23,124 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:07:23,124 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:07:23,124 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:07:23,149 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:07:23,149 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:07:23,169 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:07:23,169 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:07:23,317 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:07:23,320 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:07:23,321 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:07:23,326 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: test_enhanced.json
2025-05-30 23:09:02,062 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:09:02,063 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:09:02,063 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:09:02,069 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:09:02,069 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:09:02,069 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:09:02,069 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:09:03,043 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:09:03,093 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:09:03,093 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:09:03,098 - __main__ - INFO - Processing single file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:03,098 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:03,100 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:03,100 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:03,100 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:03,100 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:03,110 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:03,117 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:03,120 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:03,154 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:03,154 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:03,154 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:03,177 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:03,177 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:03,196 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:03,196 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:03,300 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:03,303 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:03,304 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:03,308 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: test_enhanced.json
2025-05-30 23:09:22,848 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:09:22,849 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:09:22,849 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:09:22,855 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:09:22,855 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:09:22,855 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:09:22,855 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:09:23,873 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:09:23,923 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:09:23,923 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:09:23,926 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:09:23,927 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:09:23,927 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:09:23,933 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:09:23,933 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:09:23,933 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:09:23,933 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:09:24,168 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:09:24,216 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:09:24,216 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:09:24,220 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:24,222 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,222 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,222 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,222 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,233 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,240 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,243 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,276 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,276 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,276 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:24,299 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:24,299 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:24,318 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:24,318 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:24,420 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:24,423 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:24,424 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:24,429 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:24,429 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:24,432 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,432 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,432 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,433 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,438 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,443 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,448 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,468 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,468 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,468 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:24,482 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:24,482 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:24,493 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:24,493 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:24,602 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:24,606 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:24,607 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:24,615 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:24,616 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:24,625 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,625 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,625 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,625 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,633 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,640 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,645 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,675 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,675 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,675 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:24,700 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:24,700 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:24,720 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:24,720 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:24,843 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:24,861 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:24,862 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:24,893 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:24,895 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:24,902 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:24,902 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:24,902 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:24,902 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:24,914 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:24,924 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:24,929 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:24,987 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:24,987 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:24,987 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,035 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,035 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,073 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,073 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:25,190 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:25,203 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:25,204 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:25,225 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:25,226 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:25,233 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:25,233 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:25,233 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:25,233 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:25,241 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:25,250 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:25,254 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:25,291 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:25,291 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:25,291 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,322 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,322 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,347 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,347 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:25,464 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:25,477 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:25,478 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:25,500 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:25,502 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:25,509 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:25,510 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:25,510 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:25,510 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:25,520 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:25,530 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:25,534 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:25,583 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:25,583 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:25,583 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,624 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,624 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,658 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,658 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:25,779 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:25,795 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:25,796 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:25,824 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:25,825 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:25,832 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:25,832 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:25,832 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:25,832 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:25,838 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:25,843 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:25,848 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:25,864 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:25,864 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:25,865 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:25,877 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:25,877 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:25,887 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:25,887 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,006 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,018 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,020 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,041 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:26,042 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,045 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,045 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,045 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,045 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,050 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,054 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,058 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,070 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,070 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,070 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,079 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,079 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,086 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,086 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,193 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,197 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,197 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,204 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,205 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:26,206 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,206 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,206 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,206 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,209 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,213 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,216 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,221 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,221 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,221 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,224 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,224 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,225 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,225 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,317 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,317 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,318 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,318 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:09:26,319 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,322 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,322 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,322 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,322 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,328 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,333 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,337 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,352 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,352 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,352 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,364 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,364 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,372 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,372 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,485 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,490 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,491 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,500 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:09:26,501 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:26,515 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,515 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,515 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,515 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,528 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,539 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,545 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,602 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,603 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,603 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,658 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,658 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,702 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,703 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:26,836 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:26,864 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:26,865 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:26,915 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:09:26,916 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:26,922 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:09:26,923 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:09:26,923 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:09:26,923 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:09:26,930 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:09:26,936 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:09:26,940 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:09:26,964 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:09:26,964 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:09:26,964 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:09:26,982 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:09:26,982 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:09:26,996 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:09:26,996 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:09:27,112 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:09:27,123 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:09:27,124 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:09:27,143 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:09:27,390 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
2025-05-30 23:10:23,590 - __main__ - INFO - Initializing ML-based touch data cleaner...
2025-05-30 23:10:23,590 - ML.ml_cleaning_pipeline - INFO - Initialized ML Touch Data Cleaner
2025-05-30 23:10:23,590 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:10:23,596 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:10:23,596 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:10:23,596 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:10:23,596 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:10:24,539 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:10:24,587 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:10:24,588 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:10:24,591 - __main__ - INFO - Processing directory: raw_JSONs
2025-05-30 23:10:24,591 - ML.ml_cleaning_pipeline - INFO - Processing directory: raw_JSONs
2025-05-30 23:10:24,592 - ML.ml_cleaning_pipeline - INFO - Setting up transfer learning model...
2025-05-30 23:10:24,598 - ML.transfer_learning_model - INFO - Initialized TouchDataTransferLearner on device: cpu
2025-05-30 23:10:24,598 - ML.ml_cleaning_pipeline - INFO - Pre-training new model on synthetic data...
2025-05-30 23:10:24,598 - ML.transfer_learning_model - INFO - Starting pre-training phase...
2025-05-30 23:10:24,598 - ML.transfer_learning_model - INFO - Generating 5000 synthetic touch sequences...
2025-05-30 23:10:24,832 - ML.transfer_learning_model - WARNING - Label processing error: 0, using default values
2025-05-30 23:10:24,878 - ML.ml_cleaning_pipeline - WARNING - Transfer learning setup failed: The size of tensor a (8) must match the size of tensor b (3) at non-singleton dimension 0
2025-05-30 23:10:24,878 - ML.ml_cleaning_pipeline - WARNING - Continuing without transfer learning (basic ML features still available)
2025-05-30 23:10:24,882 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:24,884 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:24,884 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:24,884 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:24,884 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:24,893 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:24,901 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:24,904 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:24,936 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:24,936 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:24,936 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:24,958 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:24,958 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:24,975 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:24,975 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,076 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,079 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,080 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,084 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_16_54.565385_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:25,085 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:25,088 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,088 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,088 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,088 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,094 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,099 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,102 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,122 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,122 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,122 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,136 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,136 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,146 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,146 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,253 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,258 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,259 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,266 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 18_55_51.243907_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:25,267 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:25,275 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,276 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,276 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,276 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,284 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,291 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,295 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,325 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,325 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,325 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,348 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,348 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,367 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,367 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,488 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,505 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,507 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,537 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_27_08.568238_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:25,539 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:25,545 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,545 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,545 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,546 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,557 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,567 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,571 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,628 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,628 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,628 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,675 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,675 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,712 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,712 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:25,827 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:25,839 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:25,841 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:25,862 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_14_47.544798_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:25,863 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:25,869 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:25,870 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:25,870 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:25,870 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:25,878 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:25,886 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:25,890 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:25,926 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:25,926 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:25,926 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:25,956 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:25,956 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:25,980 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:25,980 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,094 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,106 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,108 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,129 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_47_50.730692_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:26,130 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:26,138 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,138 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,138 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,138 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,148 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,157 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,161 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,210 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,210 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,210 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,250 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,250 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,283 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,283 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,402 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,418 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,420 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,447 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_25_28.733352_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:26,448 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,455 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,455 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,455 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,455 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,460 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,466 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,470 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,486 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,486 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,486 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,498 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,498 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,507 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,508 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,625 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,637 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,638 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,659 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_21_40.895511_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,660 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:26,662 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,663 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,663 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,663 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,667 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,672 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,675 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,687 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,687 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,687 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,696 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,696 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,702 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,702 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,806 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,810 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,811 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,817 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 23_03_55.624973_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:26,818 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,819 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,819 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,819 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,819 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,822 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,826 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,829 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,834 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,834 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,834 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,836 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,836 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,838 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,838 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:26,926 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:26,927 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:26,927 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:26,928 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-12 11_17_30.613271_61de69cf1d8fbe9557c459c4.json
2025-05-30 23:10:26,928 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:26,931 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:26,931 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:26,931 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:26,931 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:26,937 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:26,942 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:26,945 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:26,959 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:26,959 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:26,959 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:26,970 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:26,970 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:26,978 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:26,978 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:27,086 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:27,091 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:27,092 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:27,101 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2021-06-10 19_16_25.202659_60c1b314625faf1d31ad63bb.json
2025-05-30 23:10:27,102 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:27,116 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:27,116 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:27,116 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:27,116 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:27,129 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:27,139 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:27,145 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:27,202 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:27,202 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:27,202 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:27,256 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:27,256 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:27,299 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:27,299 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:27,429 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:27,457 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:27,459 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:27,507 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-11 10_30_08.697216_61dd0d1c1d8fbeb384c4589d.json
2025-05-30 23:10:27,508 - ML.ml_cleaning_pipeline - INFO - Processing JSON file: raw_JSONs/Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:27,514 - ML.ml_cleaning_pipeline - INFO - Applying ML-based data enhancement...
2025-05-30 23:10:27,514 - ML.ml_cleaning_pipeline - INFO - Step 1: Feature engineering...
2025-05-30 23:10:27,514 - ML.feature_engineering - INFO - Starting comprehensive feature extraction...
2025-05-30 23:10:27,514 - ML.feature_engineering - INFO - Extracting temporal features...
2025-05-30 23:10:27,520 - ML.feature_engineering - INFO - Extracting spatial features...
2025-05-30 23:10:27,526 - ML.feature_engineering - INFO - Extracting behavioral features...
2025-05-30 23:10:27,530 - ML.feature_engineering - INFO - Extracting sequence quality features...
2025-05-30 23:10:27,553 - ML.feature_engineering - INFO - Extracted 35 new features
2025-05-30 23:10:27,553 - ML.ml_cleaning_pipeline - INFO - Step 2: Quality assessment...
2025-05-30 23:10:27,553 - ML.metadata_enhancer - INFO - Analyzing sequence quality...
2025-05-30 23:10:27,571 - ML.ml_cleaning_pipeline - INFO - Step 3: Behavioral classification...
2025-05-30 23:10:27,571 - ML.metadata_enhancer - INFO - Classifying behavioral patterns...
2025-05-30 23:10:27,584 - ML.ml_cleaning_pipeline - INFO - Step 4: Anomaly detection...
2025-05-30 23:10:27,584 - ML.metadata_enhancer - INFO - Detecting anomalies...
2025-05-30 23:10:27,697 - ML.ml_cleaning_pipeline - INFO - Step 6: Generating usage recommendations...
2025-05-30 23:10:27,708 - ML.ml_cleaning_pipeline - INFO - ML-based enhancement completed
2025-05-30 23:10:27,709 - ML.ml_cleaning_pipeline - ERROR - Error converting DataFrame to JSON: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-30 23:10:27,728 - ML.ml_cleaning_pipeline - INFO - Enhanced data saved to: enhanced_data/enhanced_Coloring_2022-01-14 12_46_13.635337_61e122d61d8fbe7ae4c45ae7.json
2025-05-30 23:10:27,970 - ML.ml_cleaning_pipeline - INFO - Directory processing completed. 12/12 files processed successfully
