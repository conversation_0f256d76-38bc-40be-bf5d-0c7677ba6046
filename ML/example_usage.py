#!/usr/bin/env python3
"""
Example Usage of ML-Based Touch Data Cleaning

This script demonstrates how to use the ML cleaning pipeline
to enhance Coloring touchdata with metadata while preserving all original data.
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ML.ml_cleaning_pipeline import MLTouchDataCleaner

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demonstrate_single_file_processing():
    """Demonstrate processing a single JSON file."""
    print("=" * 60)
    print("DEMONSTRATION: Single File Processing")
    print("=" * 60)
    
    # Initialize the ML cleaner
    cleaner = MLTouchDataCleaner()
    
    # Find a sample file from raw_JSONs
    raw_json_dir = Path("raw_JSONs")
    if not raw_json_dir.exists():
        print("❌ raw_JSONs directory not found. Please ensure you're running from the project root.")
        return
    
    # Get the first Coloring JSON file
    json_files = list(raw_json_dir.glob("Coloring_*.json"))
    if not json_files:
        print("❌ No Coloring JSON files found in raw_JSONs directory.")
        return
    
    sample_file = json_files[0]
    print(f"📁 Processing file: {sample_file}")
    
    # Create output directory
    output_dir = Path("ML/examples")
    output_dir.mkdir(exist_ok=True)
    
    output_file = output_dir / f"enhanced_{sample_file.name}"
    
    try:
        # Process the file
        print("🔄 Starting ML-based enhancement...")
        result = cleaner.process_json_file(str(sample_file), str(output_file))
        
        if result['status'] == 'success':
            print("✅ Processing completed successfully!")
            print_processing_results(result)
            
            # Show sample of enhanced data
            show_enhancement_sample(str(output_file))
            
        else:
            print(f"❌ Processing failed: {result.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during processing: {e}")

def demonstrate_directory_processing():
    """Demonstrate processing all files in a directory."""
    print("\n" + "=" * 60)
    print("DEMONSTRATION: Directory Processing")
    print("=" * 60)
    
    # Initialize the ML cleaner
    cleaner = MLTouchDataCleaner()
    
    # Check if raw_JSONs directory exists
    input_dir = "raw_JSONs"
    if not Path(input_dir).exists():
        print("❌ raw_JSONs directory not found.")
        return
    
    # Create output directory
    output_dir = "ML/enhanced_data"
    
    try:
        print(f"📁 Processing directory: {input_dir}")
        print(f"📁 Output directory: {output_dir}")
        print("🔄 Starting batch processing...")
        
        # Process all files in the directory
        result = cleaner.process_directory(input_dir, output_dir)
        
        if result['status'] == 'completed':
            print("✅ Batch processing completed!")
            print_batch_results(result)
        else:
            print(f"❌ Batch processing failed: {result.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during batch processing: {e}")

def print_processing_results(result):
    """Print detailed results of file processing."""
    stats = result['statistics']
    
    print(f"\n📊 Processing Statistics:")
    print(f"   • Original data points: {stats['original_data_points']}")
    print(f"   • Enhanced data points: {stats['enhanced_data_points']}")
    print(f"   • Sequences processed: {stats['sequences_processed']}")
    print(f"   • Features added: {stats['features_added']}")
    
    if stats.get('quality_distribution'):
        print(f"\n🎯 Quality Distribution:")
        for tier, count in stats['quality_distribution'].items():
            print(f"   • {tier.capitalize()}: {count}")
    
    if stats.get('behavioral_patterns'):
        print(f"\n🧠 Behavioral Patterns:")
        for pattern, count in stats['behavioral_patterns'].items():
            print(f"   • {pattern.capitalize()}: {count}")
    
    if stats.get('anomaly_summary'):
        print(f"\n⚠️  Anomaly Summary:")
        for anomaly_type, count in stats['anomaly_summary'].items():
            print(f"   • {anomaly_type.capitalize()}: {count}")

def print_batch_results(result):
    """Print results of batch processing."""
    print(f"\n📊 Batch Processing Results:")
    print(f"   • Total files: {result['total_files']}")
    print(f"   • Successfully processed: {result['successful']}")
    print(f"   • Failed: {result['failed']}")
    
    if result['failed'] > 0:
        print(f"\n❌ Failed Files:")
        failed_files = [r for r in result['results'] if r['status'] != 'success']
        for failed in failed_files[:5]:  # Show first 5 failures
            print(f"   • {Path(failed.get('input_file', 'Unknown')).name}: {failed.get('message', 'Unknown error')}")
        
        if len(failed_files) > 5:
            print(f"   • ... and {len(failed_files) - 5} more")

def show_enhancement_sample(output_file):
    """Show a sample of the enhanced data structure."""
    print(f"\n🔍 Sample of Enhanced Data Structure:")
    
    try:
        with open(output_file, 'r') as f:
            enhanced_data = json.load(f)
        
        # Show ML metadata summary
        if 'ml_metadata' in enhanced_data:
            ml_meta = enhanced_data['ml_metadata']
            print(f"   📈 ML Metadata Summary:")
            print(f"      • Processing timestamp: {ml_meta.get('processing_timestamp', 'N/A')}")
            print(f"      • Total sequences: {ml_meta.get('total_sequences', 'N/A')}")
            print(f"      • Total touch points: {ml_meta.get('total_touch_points', 'N/A')}")
            
            if 'quality_distribution' in ml_meta:
                print(f"      • Quality distribution: {ml_meta['quality_distribution']}")
            
            if 'behavioral_patterns' in ml_meta:
                print(f"      • Behavioral patterns: {ml_meta['behavioral_patterns']}")
        
        # Show sample enhanced touch point
        touch_data = enhanced_data.get('json', {}).get('touchData', {})
        if touch_data:
            first_sequence = list(touch_data.values())[0]
            if first_sequence:
                sample_point = first_sequence[0]
                
                print(f"\n   🎯 Sample Enhanced Touch Point:")
                print(f"      Original data:")
                original_fields = ['x', 'y', 'time', 'touchPhase', 'fingerId']
                for field in original_fields:
                    if field in sample_point:
                        print(f"         {field}: {sample_point[field]}")
                
                print(f"      ML enhancements:")
                ml_fields = ['ml_quality_score', 'quality_tier', 'behavioral_pattern', 
                           'anomaly_score', 'usage_recommendations']
                for field in ml_fields:
                    if field in sample_point:
                        value = sample_point[field]
                        if isinstance(value, list):
                            value = ', '.join(value[:3])  # Show first 3 recommendations
                        print(f"         {field}: {value}")
        
    except Exception as e:
        print(f"   ❌ Error reading enhanced file: {e}")

def demonstrate_data_preservation():
    """Demonstrate that original data is preserved."""
    print("\n" + "=" * 60)
    print("DEMONSTRATION: Data Preservation Verification")
    print("=" * 60)
    
    # Find a sample file
    raw_json_dir = Path("raw_JSONs")
    json_files = list(raw_json_dir.glob("Coloring_*.json"))
    
    if not json_files:
        print("❌ No files available for demonstration.")
        return
    
    sample_file = json_files[0]
    
    try:
        # Load original data
        with open(sample_file, 'r') as f:
            original_data = json.load(f)
        
        # Process with ML cleaner
        cleaner = MLTouchDataCleaner()
        result = cleaner.process_json_file(str(sample_file))
        
        if result['status'] != 'success':
            print("❌ Processing failed, cannot verify preservation.")
            return
        
        enhanced_data = result['enhanced_data']
        
        # Compare original vs enhanced data
        print("🔍 Verifying data preservation...")
        
        original_touch_data = original_data.get('json', {}).get('touchData', {})
        enhanced_touch_data = enhanced_data.get('json', {}).get('touchData', {})
        
        preservation_verified = True
        
        for seq_id, original_sequence in original_touch_data.items():
            if seq_id not in enhanced_touch_data:
                print(f"❌ Sequence {seq_id} missing in enhanced data!")
                preservation_verified = False
                continue
            
            enhanced_sequence = enhanced_touch_data[seq_id]
            
            if len(original_sequence) != len(enhanced_sequence):
                print(f"❌ Sequence {seq_id} length changed!")
                preservation_verified = False
                continue
            
            # Check critical fields are preserved
            critical_fields = ['x', 'y', 'time', 'touchPhase']
            for i, (orig_point, enh_point) in enumerate(zip(original_sequence, enhanced_sequence)):
                for field in critical_fields:
                    if field in orig_point:
                        if orig_point[field] != enh_point.get(field):
                            print(f"❌ Field {field} changed in sequence {seq_id}, point {i}!")
                            preservation_verified = False
        
        if preservation_verified:
            print("✅ Data preservation verified! All original coordinates and phases preserved.")
            print("✅ ML enhancements added as metadata without modifying core data.")
        else:
            print("❌ Data preservation verification failed!")
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")

def main():
    """Main demonstration function."""
    print("🤖 ML-Based Touch Data Cleaning Demonstration")
    print("=" * 60)
    print("This demonstration shows how the ML cleaning system enhances")
    print("Coloring touchdata with rich metadata while preserving all original data.")
    print()
    
    try:
        # Demonstrate single file processing
        demonstrate_single_file_processing()
        
        # Demonstrate data preservation
        demonstrate_data_preservation()
        
        # Demonstrate directory processing (commented out to avoid processing all files)
        # demonstrate_directory_processing()
        
        print("\n" + "=" * 60)
        print("🎉 Demonstration completed!")
        print("=" * 60)
        print("Key benefits of ML-based cleaning:")
        print("✅ Preserves all original touch coordinates and timing")
        print("✅ Adds rich quality assessments and behavioral insights")
        print("✅ Provides usage recommendations for different analyses")
        print("✅ Detects anomalies without removing data")
        print("✅ Enables transfer learning for improved pattern recognition")
        print()
        print("To use the ML cleaning system:")
        print("1. Run: python ML/ml_clean_coloring_data.py --input raw_JSONs --output enhanced_data")
        print("2. Or use the MLTouchDataCleaner class directly in your code")
        
    except KeyboardInterrupt:
        print("\n❌ Demonstration interrupted by user.")
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")

if __name__ == "__main__":
    main()
