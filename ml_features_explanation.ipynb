{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎨 ML-Enhanced Coloring Touch Data: Interactive Tutorial\n", "\n", "## 🌟 Welcome to Your Smart Touch Data!\n", "\n", "Imagine you're watching someone color on a tablet. With basic data, you only know **where** they touched and **when**. But with our ML enhancement, you can understand:\n", "\n", "- 🏃‍♂️ **How fast** they're moving (like a speedometer)\n", "- 🎯 **What they're trying to do** (coloring vs selecting)\n", "- 📊 **How reliable** the data is (like a quality rating)\n", "- 🚨 **If something seems wrong** (like a smoke detector)\n", "\n", "Let's explore this together with **real data** from 47 files and 90,090 touch points!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📦 Import the tools we need (like opening a toolbox)\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from glob import glob\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 🎨 Make our charts look nice\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"🎉 All tools loaded! Ready to explore your smart touch data!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Step 1: Load Your Enhanced Data\n", "\n", "Think of this like opening a photo album, but instead of pictures, we have smart touch data!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📁 Find all our enhanced CSV files\n", "csv_files = glob('enhanced_data_CSVs/*.csv')\n", "print(f\"📊 Found {len(csv_files)} enhanced data files!\")\n", "\n", "# 📖 Load one file as an example (like opening one photo album)\n", "if csv_files:\n", "    sample_file = csv_files[0]\n", "    print(f\"\\n📄 Loading sample file: {sample_file.split('/')[-1]}\")\n", "    \n", "    # Load the data (comment='#' skips the information header)\n", "    df = pd.read_csv(sample_file, comment='#')\n", "    \n", "    print(f\"✅ Success! Loaded {len(df):,} touch points with {len(df.columns)} features\")\n", "    print(f\"📅 Data spans {df['Touchdata_id'].nunique()} different touch sequences\")\n", "else:\n", "    print(\"❌ No CSV files found. Please run the conversion script first!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Step 2: See the Transformation Magic\n", "\n", "Let's compare what we had **before** vs what we have **now**!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📋 Original basic fields (what we started with)\n", "original_fields = [\n", "    'Touchdata_id', 'event_index', 'x', 'y', 'time', 'touchPhase', \n", "    'fingerId', 'accx', 'accy', 'accz', 'color', 'zone', 'completionPerc'\n", "]\n", "\n", "# 🧠 ML-enhanced fields (what we added)\n", "ml_fields = [col for col in df.columns if col not in original_fields and col != 'source_file']\n", "\n", "print(\"🎯 THE AMAZING TRANSFORMATION:\")\n", "print(\"=\" * 40)\n", "print(f\"📊 Original basic fields: {len(original_fields)}\")\n", "print(f\"🧠 ML-enhanced fields: {len(ml_fields)}\")\n", "print(f\"📁 Total columns now: {len(df.columns)}\")\n", "print(f\"🚀 Enhancement factor: {len(ml_fields)/len(original_fields):.1f}x more intelligent!\")\n", "\n", "print(\"\\n🔍 Here's what we added:\")\n", "for i, field in enumerate(ml_fields[:10], 1):  # Show first 10 as examples\n", "    print(f\"  {i:2d}. {field}\")\n", "if len(ml_fields) > 10:\n", "    print(f\"  ... and {len(ml_fields)-10} more smart features!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎭 Step 3: Understanding the 5 Types of Intelligence\n", "\n", "Our ML enhancement added 5 types of \"intelligence\" to your touch data. Let's explore each one!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ⏱️ Type 1: Timing Intelligence (16 features)\n", "\n", "**What it does**: Understands the rhythm and speed of touches (like a music teacher analyzing tempo)\n", "\n", "**Key features**:\n", "- `velocity`: How fast the finger is moving (pixels per second)\n", "- `acceleration`: How quickly speed changes\n", "- `temporal_consistency`: How steady the timing is (0-1 scale)\n", "- `time_rhythm_consistency`: How regular the pattern is"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Let's see the timing intelligence in action!\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "fig.suptitle('⏱️ Timing Intelligence: Understanding Movement Speed & Rhythm', fontsize=14, fontweight='bold')\n", "\n", "# Chart 1: Velocity distribution (how fast people move)\n", "axes[0,0].hist(df['velocity'], bins=50, alpha=0.7, color='skyblue')\n", "axes[0,0].set_title('🏃‍♂️ Movement Speed Distribution')\n", "axes[0,0].set_xlabel('Velocity (pixels/second)')\n", "axes[0,0].set_ylabel('Number of touches')\n", "axes[0,0].axvline(df['velocity'].mean(), color='red', linestyle='--', label=f'Average: {df[\"velocity\"].mean():.0f}')\n", "axes[0,0].legend()\n", "\n", "# Chart 2: Temporal consistency (how steady the timing is)\n", "axes[0,1].hist(df['temporal_consistency'], bins=30, alpha=0.7, color='lightgreen')\n", "axes[0,1].set_title('🎵 Timing Steadiness')\n", "axes[0,1].set_xlabel('Temporal Consistency (0=chaotic, 1=perfect)')\n", "axes[0,1].set_ylabel('Number of touches')\n", "axes[0,1].axvline(0.8, color='red', linestyle='--', label='Good threshold (0.8)')\n", "axes[0,1].legend()\n", "\n", "# Chart 3: Acceleration (how speed changes)\n", "acceleration_clean = df['acceleration'][df['acceleration'].between(-1000, 1000)]  # Remove extreme outliers for visualization\n", "axes[1,0].hist(acceleration_clean, bins=50, alpha=0.7, color='orange')\n", "axes[1,0].set_title('🚗 Speed Changes (Acceleration)')\n", "axes[1,0].set_xlabel('Acceleration (pixels/second²)')\n", "axes[1,0].set_ylabel('Number of touches')\n", "\n", "# Chart 4: Time between touches\n", "time_diff_clean = df['time_diff'][df['time_diff'].between(0, 0.2)]  # Focus on normal ranges\n", "axes[1,1].hist(time_diff_clean, bins=30, alpha=0.7, color='purple')\n", "axes[1,1].set_title('⏰ Time Between Touches')\n", "axes[1,1].set_xlabel('Time Difference (seconds)')\n", "axes[1,1].set_ylabel('Number of touches')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"💡 What this tells us:\")\n", "print(f\"   🏃‍♂️ Average movement speed: {df['velocity'].mean():.0f} pixels/second\")\n", "print(f\"   🎵 {(df['temporal_consistency'] >= 0.8).sum()/len(df)*100:.1f}% of touches have steady timing\")\n", "print(f\"   ⏰ Average time between touches: {df['time_diff'].mean():.3f} seconds\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📍 Type 2: Movement Intelligence (12 features)\n", "\n", "**What it does**: Tracks where and how users move (like a GPS for finger movements)\n", "\n", "**Key features**:\n", "- `distance`: How far the finger moved between touches\n", "- `direction_angle`: Which direction they're moving\n", "- `spatial_consistency`: How smooth the movement is\n", "- `cumulative_distance`: Total distance traveled in a sequence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Let's visualize movement intelligence!\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "fig.suptitle('📍 Movement Intelligence: Understanding Finger Navigation', fontsize=14, fontweight='bold')\n", "\n", "# Chart 1: Distance between touches\n", "distance_clean = df['distance'][df['distance'].between(0, 100)]  # Focus on normal movements\n", "axes[0,0].hist(distance_clean, bins=30, alpha=0.7, color='lightcoral')\n", "axes[0,0].set_title('📏 Movement Distance')\n", "axes[0,0].set_xlabel('Distance (pixels)')\n", "axes[0,0].set_ylabel('Number of touches')\n", "axes[0,0].axvline(distance_clean.mean(), color='red', linestyle='--', label=f'Average: {distance_clean.mean():.1f}')\n", "axes[0,0].legend()\n", "\n", "# Chart 2: Spatial consistency (how smooth the movement is)\n", "axes[0,1].hist(df['spatial_consistency'], bins=30, alpha=0.7, color='lightblue')\n", "axes[0,1].set_title('🌊 Movement Smoothness')\n", "axes[0,1].set_xlabel('Spatial Consistency (0=jerky, 1=smooth)')\n", "axes[0,1].set_ylabel('Number of touches')\n", "axes[0,1].axvline(0.5, color='red', linestyle='--', label='Good threshold (0.5)')\n", "axes[0,1].legend()\n", "\n", "# Chart 3: Direction changes (how much users change direction)\n", "direction_clean = df['direction_change'][df['direction_change'].between(-3, 3)]  # Remove extreme outliers\n", "axes[1,0].hist(direction_clean, bins=30, alpha=0.7, color='gold')\n", "axes[1,0].set_title('🔄 Direction Changes')\n", "axes[1,0].set_xlabel('Direction Change (radians)')\n", "axes[1,0].set_ylabel('Number of touches')\n", "\n", "# Chart 4: Movement area (bounding box)\n", "area_clean = df['bbox_area'][df['bbox_area'].between(0, 10000)]  # Focus on reasonable areas\n", "axes[1,1].hist(area_clean, bins=30, alpha=0.7, color='lightgreen')\n", "axes[1,1].set_title('📐 Movement Area')\n", "axes[1,1].set_xlabel('Bounding Box Area (pixels²)')\n", "axes[1,1].set_ylabel('Number of touches')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"💡 What this tells us:\")\n", "print(f\"   📏 Average movement distance: {distance_clean.mean():.1f} pixels\")\n", "print(f\"   🌊 {(df['spatial_consistency'] >= 0.5).sum()/len(df)*100:.1f}% of touches have smooth movement\")\n", "print(f\"   📐 Average movement area: {area_clean.mean():.0f} pixels²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🎭 Type 3: Behavior Intelligence (8 features)\n", "\n", "**What it does**: Identifies what users are trying to accomplish (like a mind reader for user intentions)\n", "\n", "**Key features**:\n", "- `behavioral_pattern`: What type of action (drag, tap, hold, complex)\n", "- `interaction_style`: How they're doing it (deliberate, quick, irregular)\n", "- `user_intent_confidence`: How sure we are about their goal (0-1 scale)\n", "- `movement_type`: The style of movement (stationary, variable, minimal)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Let's explore behavior intelligence!\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "fig.suptitle('🎭 Behavior Intelligence: Understanding User Intentions', fontsize=14, fontweight='bold')\n", "\n", "# Chart 1: Behavioral patterns (what users are doing)\n", "behavior_counts = df['behavioral_pattern'].value_counts()\n", "colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold', 'purple']\n", "axes[0,0].pie(behavior_counts.values, labels=behavior_counts.index, autopct='%1.1f%%', colors=colors[:len(behavior_counts)])\n", "axes[0,0].set_title('🎯 What Users Are Doing')\n", "\n", "# Chart 2: Interaction styles (how they're doing it)\n", "style_counts = df['interaction_style'].value_counts()\n", "axes[0,1].bar(style_counts.index, style_counts.values, color='lightblue', alpha=0.7)\n", "axes[0,1].set_title('🎨 How Users Interact')\n", "axes[0,1].set_xlabel('Interaction Style')\n", "axes[0,1].set_ylabel('Number of touches')\n", "axes[0,1].tick_params(axis='x', rotation=45)\n", "\n", "# Chart 3: User intent confidence (how sure we are)\n", "axes[1,0].hist(df['user_intent_confidence'], bins=20, alpha=0.7, color='orange')\n", "axes[1,0].set_title('🎯 Confidence in User Intent')\n", "axes[1,0].set_xlabel('Confidence (0=unsure, 1=very sure)')\n", "axes[1,0].set_ylabel('Number of touches')\n", "axes[1,0].axvline(0.7, color='red', linestyle='--', label='High confidence (0.7+)')\n", "axes[1,0].legend()\n", "\n", "# Chart 4: Movement types\n", "movement_counts = df['movement_type'].value_counts()\n", "axes[1,1].bar(movement_counts.index, movement_counts.values, color='lightgreen', alpha=0.7)\n", "axes[1,1].set_title('🏃‍♂️ Types of Movement')\n", "axes[1,1].set_xlabel('Movement Type')\n", "axes[1,1].set_ylabel('Number of touches')\n", "axes[1,1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"💡 What this tells us:\")\n", "print(f\"   🎯 Most common behavior: {behavior_counts.index[0]} ({behavior_counts.iloc[0]/len(df)*100:.1f}%)\")\n", "print(f\"   🎨 Most common style: {style_counts.index[0]} ({style_counts.iloc[0]/len(df)*100:.1f}%)\")\n", "print(f\"   🎯 {(df['user_intent_confidence'] >= 0.7).sum()/len(df)*100:.1f}% of touches have high confidence\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ✅ Type 4: Quality Intelligence (7 features)\n", "\n", "**What it does**: Rates how reliable each data point is (like a report card for data quality)\n", "\n", "**Key features**:\n", "- `ml_quality_score`: Overall quality rating (0-1 scale, like a percentage)\n", "- `quality_tier`: Simple rating (high/medium/low)\n", "- `sequence_completeness`: How complete the touch sequence is\n", "- `sequence_valid_pattern`: Whether the touch follows expected patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Let's examine data quality intelligence!\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "fig.suptitle('✅ Quality Intelligence: Rating Data Reliability', fontsize=14, fontweight='bold')\n", "\n", "# Chart 1: Quality score distribution\n", "axes[0,0].hist(df['ml_quality_score'], bins=20, alpha=0.7, color='lightgreen')\n", "axes[0,0].set_title('📊 Quality Score Distribution')\n", "axes[0,0].set_xlabel('Quality Score (0=poor, 1=excellent)')\n", "axes[0,0].set_ylabel('Number of touches')\n", "axes[0,0].axvline(0.8, color='red', linestyle='--', label='High quality (0.8+)')\n", "axes[0,0].axvline(0.5, color='orange', linestyle='--', label='Medium quality (0.5+)')\n", "axes[0,0].legend()\n", "\n", "# Chart 2: Quality tiers (simple categories)\n", "quality_counts = df['quality_tier'].value_counts()\n", "colors = ['green', 'orange', 'red']\n", "axes[0,1].pie(quality_counts.values, labels=quality_counts.index, autopct='%1.1f%%', \n", "              colors=colors[:len(quality_counts)])\n", "axes[0,1].set_title('🏆 Quality Tier Breakdown')\n", "\n", "# Chart 3: Sequence completeness\n", "axes[1,0].hist(df['sequence_completeness'], bins=20, alpha=0.7, color='skyblue')\n", "axes[1,0].set_title('📋 Sequence Completeness')\n", "axes[1,0].set_xlabel('Completeness (0=incomplete, 1=complete)')\n", "axes[1,0].set_ylabel('Number of touches')\n", "axes[1,0].axvline(1.0, color='red', linestyle='--', label='Perfect (1.0)')\n", "axes[1,0].legend()\n", "\n", "# Chart 4: Valid vs invalid patterns\n", "pattern_counts = df['sequence_valid_pattern'].value_counts()\n", "pattern_labels = ['<PERSON><PERSON><PERSON> Pat<PERSON>', '<PERSON><PERSON> Pat<PERSON>']\n", "axes[1,1].pie(pattern_counts.values, labels=pattern_labels, autopct='%1.1f%%', \n", "              colors=['lightcoral', 'lightgreen'])\n", "axes[1,1].set_title('✅ Pattern Validity')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate quality statistics\n", "high_quality = (df['ml_quality_score'] >= 0.8).sum()\n", "medium_quality = ((df['ml_quality_score'] >= 0.5) & (df['ml_quality_score'] < 0.8)).sum()\n", "low_quality = (df['ml_quality_score'] < 0.5).sum()\n", "\n", "print(\"💡 Quality Report Card:\")\n", "print(f\"   🟢 High Quality (0.8+): {high_quality:,} touches ({high_quality/len(df)*100:.1f}%)\")\n", "print(f\"   🟡 Medium Quality (0.5-0.8): {medium_quality:,} touches ({medium_quality/len(df)*100:.1f}%)\")\n", "print(f\"   🔴 Low Quality (<0.5): {low_quality:,} touches ({low_quality/len(df)*100:.1f}%)\")\n", "print(f\"   📊 Average Quality Score: {df['ml_quality_score'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🚨 Type 5: Problem Detection (5 features)\n", "\n", "**What it does**: Finds unusual or problematic data (like a security system for your data)\n", "\n", "**Key features**:\n", "- `anomaly_score`: How unusual this touch is (0=normal, 1=very weird)\n", "- `anomaly_type`: Simple classification (normal vs outlier)\n", "- `velocity_outlier`: Is the speed unusually fast/slow?\n", "- `distance_outlier`: Is the movement distance extreme?\n", "- `time_gap_outlier`: Are there unusual timing gaps?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Let's investigate problem detection!\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "fig.suptitle('🚨 Problem Detection: Finding Unusual Data', fontsize=14, fontweight='bold')\n", "\n", "# Chart 1: Anomaly score distribution\n", "axes[0,0].hist(df['anomaly_score'], bins=20, alpha=0.7, color='orange')\n", "axes[0,0].set_title('🔍 Anomaly Score Distribution')\n", "axes[0,0].set_xlabel('Anomaly Score (0=normal, 1=very unusual)')\n", "axes[0,0].set_ylabel('Number of touches')\n", "axes[0,0].axvline(0.55, color='red', linestyle='--', label='Suspicious (0.55+)')\n", "axes[0,0].legend()\n", "\n", "# Chart 2: Normal vs outlier classification\n", "anomaly_counts = df['anomaly_type'].value_counts()\n", "axes[0,1].pie(anomaly_counts.values, labels=anomaly_counts.index, autopct='%1.1f%%', \n", "              colors=['lightgreen', 'lightcoral'])\n", "axes[0,1].set_title('🎯 Normal vs Outlier')\n", "\n", "# Chart 3: Types of outliers detected\n", "outlier_types = {\n", "    'Velocity Outliers': df['velocity_outlier'].sum(),\n", "    'Distance Outliers': df['distance_outlier'].sum(),\n", "    'Time Gap Outliers': df['time_gap_outlier'].sum()\n", "}\n", "axes[1,0].bar(outlier_types.keys(), outlier_types.values(), color=['red', 'orange', 'yellow'], alpha=0.7)\n", "axes[1,0].set_title('🚨 Types of Problems Found')\n", "axes[1,0].set_ylabel('Number of outliers')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "\n", "# Chart 4: Anomaly score vs quality score relationship\n", "axes[1,1].scatter(df['anomaly_score'], df['ml_quality_score'], alpha=0.5, s=10)\n", "axes[1,1].set_title('🔗 Anomaly vs Quality Relationship')\n", "axes[1,1].set_xlabel('Anomaly Score (higher = more unusual)')\n", "axes[1,1].set_ylabel('Quality Score (higher = better quality)')\n", "axes[1,1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate anomaly statistics\n", "total_outliers = (df['anomaly_type'] == 'outlier').sum()\n", "high_anomaly = (df['anomaly_score'] > 0.8).sum()\n", "\n", "print(\"💡 Problem Detection Report:\")\n", "print(f\"   🚨 Total outliers detected: {total_outliers:,} ({total_outliers/len(df)*100:.1f}%)\")\n", "print(f\"   ⚡ Velocity outliers: {df['velocity_outlier'].sum():,}\")\n", "print(f\"   📏 Distance outliers: {df['distance_outlier'].sum():,}\")\n", "print(f\"   ⏰ Time gap outliers: {df['time_gap_outlier'].sum():,}\")\n", "print(f\"   🔥 High anomaly scores (>0.8): {high_anomaly:,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Step 4: Practical Filtering Examples\n", "\n", "Now let's learn how to filter your data for different research needs!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🎯 PRACTICAL FILTERING GUIDE\")\n", "print(\"=\" * 40)\n", "print(f\"📊 Starting with {len(df):,} total touch points\\n\")\n", "\n", "# Filter 1: High quality data only\n", "high_quality_data = df[\n", "    (df['ml_quality_score'] >= 0.8) &\n", "    (df['quality_tier'] == 'high') &\n", "    (df['sequence_valid_pattern'] == 1)\n", "]\n", "print(f\"🟢 High Quality Filter:\")\n", "print(f\"   Criteria: Quality score ≥ 0.8, High tier, Valid patterns\")\n", "print(f\"   Result: {len(high_quality_data):,} points ({len(high_quality_data)/len(df)*100:.1f}% retained)\\n\")\n", "\n", "# Filter 2: Research-grade data\n", "research_data = df[\n", "    (df['ml_quality_score'] >= 0.85) &\n", "    (df['temporal_consistency'] >= 0.9) &\n", "    (df['spatial_consistency'] >= 0.6) &\n", "    (df['anomaly_type'] == 'normal') &\n", "    (df['behavioral_pattern'].isin(['drag', 'tap', 'hold']))\n", "]\n", "print(f\"🔬 Research-Grade Filter:\")\n", "print(f\"   Criteria: Excellent quality + consistency + normal behavior\")\n", "print(f\"   Result: {len(research_data):,} points ({len(research_data)/len(df)*100:.1f}% retained)\\n\")\n", "\n", "# Filter 3: Timing analysis ready\n", "timing_data = df[\n", "    (df['temporal_consistency'] >= 0.8) &\n", "    (df['time_gap_outlier'] == 0) &\n", "    (df['ml_quality_score'] >= 0.8) &\n", "    (df['time_diff'] >= 0)\n", "]\n", "print(f\"⏱️ Timing Analysis Filter:\")\n", "print(f\"   Criteria: Good timing consistency + no time gaps + positive time diffs\")\n", "print(f\"   Result: {len(timing_data):,} points ({len(timing_data)/len(df)*100:.1f}% retained)\\n\")\n", "\n", "# Filter 4: Spatial analysis ready\n", "spatial_data = df[\n", "    (df['spatial_consistency'] >= 0.5) &\n", "    (df['distance_outlier'] == 0) &\n", "    (df['velocity_outlier'] == 0) &\n", "    (df['velocity'] <= 5000)\n", "]\n", "print(f\"📍 Spatial Analysis Filter:\")\n", "print(f\"   Criteria: Good spatial consistency + no movement outliers + reasonable speed\")\n", "print(f\"   Result: {len(spatial_data):,} points ({len(spatial_data)/len(df)*100:.1f}% retained)\\n\")\n", "\n", "# Show behavior breakdown for research data\n", "if len(research_data) > 0:\n", "    behavior_breakdown = research_data['behavioral_pattern'].value_counts()\n", "    print(f\"🎭 Research Data Behavior Breakdown:\")\n", "    for behavior, count in behavior_breakdown.items():\n", "        print(f\"   {behavior}: {count:,} ({count/len(research_data)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Step 5: Working with Multiple Files\n", "\n", "Let's combine data from multiple files to create a larger dataset!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📁 Load multiple files (let's use first 5 as an example)\n", "print(\"📁 COMBINING MULTIPLE FILES\")\n", "print(\"=\" * 30)\n", "\n", "all_data = []\n", "file_info = []\n", "\n", "# Load first 5 files as an example\n", "for i, csv_file in enumerate(csv_files[:5]):\n", "    try:\n", "        df_temp = pd.read_csv(csv_file, comment='#')\n", "        all_data.append(df_temp)\n", "        \n", "        filename = csv_file.split('/')[-1]\n", "        file_info.append({\n", "            'file': filename[:50] + '...' if len(filename) > 50 else filename,\n", "            'points': len(df_temp),\n", "            'sequences': df_temp['Touchdata_id'].nunique(),\n", "            'quality_avg': df_temp['ml_quality_score'].mean()\n", "        })\n", "        \n", "        print(f\"📄 File {i+1}: {len(df_temp):,} points, {df_temp['Touchdata_id'].nunique()} sequences\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading {csv_file}: {e}\")\n", "\n", "if all_data:\n", "    # Combine all data\n", "    combined_df = pd.concat(all_data, ignore_index=True)\n", "    \n", "    print(f\"\\n✅ COMBINED DATASET SUMMARY:\")\n", "    print(f\"   📊 Total files: {len(all_data)}\")\n", "    print(f\"   📈 Total touch points: {len(combined_df):,}\")\n", "    print(f\"   🔗 Total sequences: {combined_df['Touchdata_id'].nunique():,}\")\n", "    print(f\"   👥 Unique users: {combined_df['source_file'].nunique()}\")\n", "    print(f\"   📊 Average quality: {combined_df['ml_quality_score'].mean():.3f}\")\n", "    \n", "    # Show quality distribution for combined data\n", "    quality_dist = combined_df['quality_tier'].value_counts()\n", "    print(f\"\\n🏆 Combined Quality Distribution:\")\n", "    for tier, count in quality_dist.items():\n", "        print(f\"   {tier}: {count:,} ({count/len(combined_df)*100:.1f}%)\")\n", "    \n", "    # Show behavior distribution for combined data\n", "    behavior_dist = combined_df['behavioral_pattern'].value_counts()\n", "    print(f\"\\n🎭 Combined Behavior Distribution:\")\n", "    for behavior, count in behavior_dist.items():\n", "        print(f\"   {behavior}: {count:,} ({count/len(combined_df)*100:.1f}%)\")\n", "else:\n", "    print(\"❌ No files could be loaded!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Step 6: Saving Your Filtered Data\n", "\n", "Learn how to save your filtered datasets for future use!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 💾 Save different filtered versions\n", "print(\"💾 SAVING FILTERED DATASETS\")\n", "print(\"=\" * 30)\n", "\n", "if 'combined_df' in locals():\n", "    # Create different filtered versions\n", "    datasets_to_save = {\n", "        'high_quality_coloring_data.csv': combined_df[\n", "            (combined_df['ml_quality_score'] >= 0.8) &\n", "            (combined_df['quality_tier'] == 'high')\n", "        ],\n", "        'research_grade_coloring_data.csv': combined_df[\n", "            (combined_df['ml_quality_score'] >= 0.85) &\n", "            (combined_df['temporal_consistency'] >= 0.9) &\n", "            (combined_df['spatial_consistency'] >= 0.6) &\n", "            (combined_df['anomaly_type'] == 'normal')\n", "        ],\n", "        'timing_analysis_ready.csv': combined_df[\n", "            (combined_df['temporal_consistency'] >= 0.8) &\n", "            (combined_df['time_gap_outlier'] == 0) &\n", "            (combined_df['ml_quality_score'] >= 0.8)\n", "        ],\n", "        'spatial_analysis_ready.csv': combined_df[\n", "            (combined_df['spatial_consistency'] >= 0.5) &\n", "            (combined_df['distance_outlier'] == 0) &\n", "            (combined_df['velocity_outlier'] == 0)\n", "        ]\n", "    }\n", "    \n", "    for filename, filtered_data in datasets_to_save.items():\n", "        if len(filtered_data) > 0:\n", "            filtered_data.to_csv(filename, index=False)\n", "            retention_rate = len(filtered_data) / len(combined_df) * 100\n", "            print(f\"✅ Saved {filename}:\")\n", "            print(f\"   📊 {len(filtered_data):,} points ({retention_rate:.1f}% retention)\")\n", "            print(f\"   🔗 {filtered_data['Touchdata_id'].nunique()} sequences\")\n", "            print(f\"   📈 Avg quality: {filtered_data['ml_quality_score'].mean():.3f}\\n\")\n", "        else:\n", "            print(f\"⚠️ {filename}: No data passed the filter!\\n\")\n", "    \n", "    print(\"🎯 How to load these files later:\")\n", "    print(\"   df = pd.read_csv('high_quality_coloring_data.csv')\")\n", "    print(\"   research_df = pd.read_csv('research_grade_coloring_data.csv')\")\n", "else:\n", "    print(\"❌ No combined data available to save!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎓 Step 7: Your Next Steps\n", "\n", "Congratulations! You now understand how to work with ML-enhanced touch data. Here's what you can do next:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🚀 **What You've Learned:**\n", "\n", "1. **📊 Data Transformation**: How we turned 13 basic fields into 62 intelligent features\n", "2. **🧠 5 Types of Intelligence**: Timing, Movement, Behavior, Quality, and Problem Detection\n", "3. **🔍 Quality Assessment**: How to identify reliable vs unreliable data\n", "4. **🎯 Smart Filtering**: How to select data for different research needs\n", "5. **📁 Multi-file Analysis**: How to combine datasets for larger studies\n", "\n", "### 🎯 **Ready-to-Use Datasets:**\n", "\n", "- **📊 47 Enhanced CSV files** in `enhanced_data_CSVs/` directory\n", "- **90,090 total touch points** with 62 features each\n", "- **81.9% high-quality data** ready for research\n", "- **Multiple filtered versions** saved for specific analyses\n", "\n", "### 🔬 **Research Applications:**\n", "\n", "1. **User Behavior Studies**: Analyze how people interact with mobile apps\n", "2. **Motor Skill Assessment**: Study finger dexterity and coordination\n", "3. **Interface Design**: Understand optimal touch target sizes and layouts\n", "4. **Accessibility Research**: Examine touch patterns for different user groups\n", "5. **Data Quality Research**: Investigate touch sensor reliability\n", "\n", "### 📚 **Continue Learning:**\n", "\n", "1. **Explore visualizations**: Create charts showing user behavior patterns\n", "2. **Statistical analysis**: Compare different user groups or time periods\n", "3. **Machine learning**: Build models to predict user intentions\n", "4. **Time series analysis**: Study how behavior changes over time\n", "5. **Spatial analysis**: Map movement patterns and heat maps\n", "\n", "### 🎉 **You're Ready!**\n", "\n", "You now have the knowledge and tools to analyze touch data like a pro. Your enhanced dataset contains insights that were impossible to see in the raw data. Happy analyzing! 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}