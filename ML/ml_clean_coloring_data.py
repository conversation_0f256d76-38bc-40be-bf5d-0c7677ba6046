#!/usr/bin/env python3
"""
ML-Based Coloring Data Cleaning Script

This script provides a command-line interface for cleaning Coloring touchdata
using machine learning techniques. It preserves all original data while adding
rich metadata and quality assessments.

Usage:
    python ML/ml_clean_coloring_data.py --input raw_JSONs --output cleaned_JSONs
    python ML/ml_clean_coloring_data.py --file raw_JSONs/specific_file.json --output cleaned_file.json
    python ML/ml_clean_coloring_data.py --retrain  # Force retrain models
"""

import argparse
import logging
import sys
import os
from pathlib import Path
import json
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ML.ml_cleaning_pipeline import MLTouchDataCleaner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ML/ml_cleaning.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function for ML-based Coloring data cleaning."""
    parser = argparse.ArgumentParser(
        description="ML-Based Coloring Touch Data Cleaning",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all JSON files in a directory
  python ML/ml_clean_coloring_data.py --input raw_JSONs --output cleaned_JSONs
  
  # Process a single file
  python ML/ml_clean_coloring_data.py --file raw_JSONs/Coloring_2022-01-12.json --output cleaned_file.json
  
  # Force retrain models
  python ML/ml_clean_coloring_data.py --input raw_JSONs --output cleaned_JSONs --retrain
  
  # Generate report only (no output files)
  python ML/ml_clean_coloring_data.py --input raw_JSONs --report-only
        """
    )
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        '--input', '-i',
        type=str,
        help='Input directory containing JSON files'
    )
    input_group.add_argument(
        '--file', '-f',
        type=str,
        help='Single JSON file to process'
    )
    
    # Output options
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output directory or file path'
    )
    
    # Processing options
    parser.add_argument(
        '--retrain',
        action='store_true',
        help='Force retrain ML models even if they exist'
    )
    
    parser.add_argument(
        '--report-only',
        action='store_true',
        help='Generate analysis report only, do not save enhanced files'
    )
    
    parser.add_argument(
        '--pattern',
        type=str,
        default='Coloring_*.json',
        help='File pattern to match (default: Coloring_*.json)'
    )
    
    parser.add_argument(
        '--model-dir',
        type=str,
        default='ML/models',
        help='Directory to store ML models (default: ML/models)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate arguments
    if not args.report_only and not args.output:
        parser.error("--output is required unless --report-only is specified")
    
    # Initialize ML cleaner
    logger.info("Initializing ML-based touch data cleaner...")
    try:
        cleaner = MLTouchDataCleaner(model_dir=args.model_dir)
        
        # Setup transfer learning
        cleaner.setup_transfer_learning(force_retrain=args.retrain)
        
    except Exception as e:
        logger.error(f"Failed to initialize ML cleaner: {e}")
        return 1
    
    # Process data
    try:
        if args.file:
            # Process single file
            result = process_single_file(cleaner, args.file, args.output, args.report_only)
        else:
            # Process directory
            result = process_directory(cleaner, args.input, args.output, args.pattern, args.report_only)
        
        # Print summary
        print_summary(result)
        
        return 0 if result.get('status') in ['success', 'completed'] else 1
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        return 1

def process_single_file(cleaner, input_file, output_file, report_only):
    """Process a single JSON file."""
    logger.info(f"Processing single file: {input_file}")
    
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file not found: {input_file}")
    
    # Determine output file if not provided
    if not report_only and not output_file:
        input_path = Path(input_file)
        output_file = input_path.parent / f"enhanced_{input_path.name}"
    
    # Process the file
    result = cleaner.process_json_file(
        input_file, 
        output_file if not report_only else None
    )
    
    # Generate report
    if result['status'] == 'success':
        generate_file_report(result, input_file)
    
    return result

def process_directory(cleaner, input_dir, output_dir, pattern, report_only):
    """Process all matching files in a directory."""
    logger.info(f"Processing directory: {input_dir}")
    
    if not os.path.exists(input_dir):
        raise FileNotFoundError(f"Input directory not found: {input_dir}")
    
    # Determine output directory
    if not report_only and not output_dir:
        output_dir = f"enhanced_{input_dir}"
    
    # Process the directory
    result = cleaner.process_directory(
        input_dir,
        output_dir if not report_only else "ML/temp_reports",
        pattern
    )
    
    # Generate comprehensive report
    if result['status'] == 'completed':
        generate_directory_report(result, input_dir, output_dir)
    
    return result

def generate_file_report(result, input_file):
    """Generate a report for a single file processing."""
    print(f"\n{'='*60}")
    print(f"ML CLEANING REPORT - SINGLE FILE")
    print(f"{'='*60}")
    print(f"Input File: {input_file}")
    print(f"Status: {result['status'].upper()}")
    
    if result['status'] == 'success':
        stats = result['statistics']
        print(f"Output File: {result.get('output_file', 'Report only')}")
        print(f"\nData Statistics:")
        print(f"  Original data points: {stats['original_data_points']}")
        print(f"  Enhanced data points: {stats['enhanced_data_points']}")
        print(f"  Sequences processed: {stats['sequences_processed']}")
        print(f"  Features added: {stats['features_added']}")
        
        print(f"\nQuality Distribution:")
        for tier, count in stats.get('quality_distribution', {}).items():
            print(f"  {tier.capitalize()}: {count}")
        
        print(f"\nBehavioral Patterns:")
        for pattern, count in stats.get('behavioral_patterns', {}).items():
            print(f"  {pattern.capitalize()}: {count}")
        
        print(f"\nAnomaly Summary:")
        for anomaly_type, count in stats.get('anomaly_summary', {}).items():
            print(f"  {anomaly_type.capitalize()}: {count}")
    
    else:
        print(f"Error: {result.get('message', 'Unknown error')}")

def generate_directory_report(result, input_dir, output_dir):
    """Generate a comprehensive report for directory processing."""
    print(f"\n{'='*60}")
    print(f"ML CLEANING REPORT - DIRECTORY")
    print(f"{'='*60}")
    print(f"Input Directory: {input_dir}")
    print(f"Output Directory: {output_dir}")
    print(f"Processing Status: {result['status'].upper()}")
    print(f"Files Processed: {result['successful']}/{result['total_files']}")
    
    if result['failed'] > 0:
        print(f"Failed Files: {result['failed']}")
    
    # Aggregate statistics
    all_stats = [r['statistics'] for r in result['results'] if r['status'] == 'success']
    
    if all_stats:
        print(f"\nAggregated Statistics:")
        total_data_points = sum(s['original_data_points'] for s in all_stats)
        total_sequences = sum(s['sequences_processed'] for s in all_stats)
        total_features = sum(s['features_added'] for s in all_stats)
        
        print(f"  Total data points processed: {total_data_points}")
        print(f"  Total sequences processed: {total_sequences}")
        print(f"  Total features added: {total_features}")
        
        # Aggregate quality distribution
        quality_totals = {}
        for stats in all_stats:
            for tier, count in stats.get('quality_distribution', {}).items():
                quality_totals[tier] = quality_totals.get(tier, 0) + count
        
        if quality_totals:
            print(f"\nOverall Quality Distribution:")
            for tier, count in quality_totals.items():
                percentage = (count / total_data_points) * 100 if total_data_points > 0 else 0
                print(f"  {tier.capitalize()}: {count} ({percentage:.1f}%)")
        
        # Aggregate behavioral patterns
        pattern_totals = {}
        for stats in all_stats:
            for pattern, count in stats.get('behavioral_patterns', {}).items():
                pattern_totals[pattern] = pattern_totals.get(pattern, 0) + count
        
        if pattern_totals:
            print(f"\nOverall Behavioral Patterns:")
            for pattern, count in pattern_totals.items():
                percentage = (count / total_sequences) * 100 if total_sequences > 0 else 0
                print(f"  {pattern.capitalize()}: {count} ({percentage:.1f}%)")
    
    # List failed files if any
    failed_files = [r for r in result['results'] if r['status'] != 'success']
    if failed_files:
        print(f"\nFailed Files:")
        for failed in failed_files:
            print(f"  {failed.get('input_file', 'Unknown')}: {failed.get('message', 'Unknown error')}")

def print_summary(result):
    """Print a brief summary of the processing results."""
    print(f"\n{'='*60}")
    print(f"PROCESSING SUMMARY")
    print(f"{'='*60}")
    
    if result.get('status') == 'success':
        print("✓ Single file processed successfully")
    elif result.get('status') == 'completed':
        print(f"✓ Directory processing completed")
        print(f"  Success: {result['successful']}/{result['total_files']} files")
        if result['failed'] > 0:
            print(f"  Failed: {result['failed']} files")
    else:
        print("✗ Processing failed")
        print(f"  Error: {result.get('message', 'Unknown error')}")
    
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    sys.exit(main())
