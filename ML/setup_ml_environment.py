#!/usr/bin/env python3
"""
Setup Script for ML-Based Touch Data Cleaning Environment

This script sets up the required environment for ML-based touch data cleaning,
including dependency installation and model directory creation.
"""

import subprocess
import sys
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8 or higher is required for ML components")
        return False
    
    logger.info(f"Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required dependencies for ML components."""
    logger.info("Installing ML dependencies...")
    
    # Core ML dependencies that are essential
    core_dependencies = [
        "scikit-learn>=1.3.0",
        "scipy>=1.10.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "joblib>=1.3.0",
        "tqdm>=4.65.0",
        "jsonschema>=4.17.0",
        "pydantic>=2.0.0"
    ]
    
    # Optional dependencies (PyTorch and Transformers)
    optional_dependencies = [
        "torch>=2.0.0",
        "transformers>=4.30.0"
    ]
    
    # Install core dependencies
    for dep in core_dependencies:
        try:
            logger.info(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"✅ Successfully installed {dep}")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install {dep}: {e}")
            return False
    
    # Try to install optional dependencies
    for dep in optional_dependencies:
        try:
            logger.info(f"Installing optional dependency {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"✅ Successfully installed {dep}")
        except subprocess.CalledProcessError as e:
            logger.warning(f"⚠️ Failed to install optional dependency {dep}: {e}")
            logger.warning("Transfer learning features may be limited without PyTorch")
    
    return True

def create_directories():
    """Create necessary directories for ML components."""
    logger.info("Creating ML directories...")
    
    directories = [
        "ML/models",
        "ML/examples",
        "ML/temp_reports",
        "ML/enhanced_data"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ Created directory: {directory}")
        except Exception as e:
            logger.error(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True

def test_imports():
    """Test that all required modules can be imported."""
    logger.info("Testing module imports...")
    
    # Test core dependencies
    core_modules = [
        ("pandas", "pd"),
        ("numpy", "np"),
        ("sklearn", None),
        ("scipy", None),
        ("matplotlib", None),
        ("seaborn", None),
        ("joblib", None),
        ("tqdm", None),
        ("jsonschema", None),
        ("pydantic", None)
    ]
    
    for module_name, alias in core_modules:
        try:
            if alias:
                exec(f"import {module_name} as {alias}")
            else:
                exec(f"import {module_name}")
            logger.info(f"✅ Successfully imported {module_name}")
        except ImportError as e:
            logger.error(f"❌ Failed to import {module_name}: {e}")
            return False
    
    # Test optional dependencies
    optional_modules = [
        ("torch", None),
        ("transformers", None)
    ]
    
    for module_name, alias in optional_modules:
        try:
            if alias:
                exec(f"import {module_name} as {alias}")
            else:
                exec(f"import {module_name}")
            logger.info(f"✅ Successfully imported optional module {module_name}")
        except ImportError as e:
            logger.warning(f"⚠️ Optional module {module_name} not available: {e}")
    
    return True

def test_ml_components():
    """Test that ML components can be imported and initialized."""
    logger.info("Testing ML components...")
    
    try:
        # Test feature engineering
        from ML.feature_engineering import TouchFeatureEngineer
        feature_engineer = TouchFeatureEngineer()
        logger.info("✅ TouchFeatureEngineer initialized successfully")
        
        # Test metadata enhancer
        from ML.metadata_enhancer import TouchDataMetadataEnhancer
        metadata_enhancer = TouchDataMetadataEnhancer()
        logger.info("✅ TouchDataMetadataEnhancer initialized successfully")
        
        # Test main pipeline
        from ML.ml_cleaning_pipeline import MLTouchDataCleaner
        cleaner = MLTouchDataCleaner()
        logger.info("✅ MLTouchDataCleaner initialized successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize ML components: {e}")
        return False

def create_example_config():
    """Create an example configuration file."""
    logger.info("Creating example configuration...")
    
    config = {
        "ml_settings": {
            "model_dir": "ML/models",
            "enable_transfer_learning": True,
            "pretrain_epochs": 30,
            "batch_size": 16,
            "learning_rate": 0.001
        },
        "feature_engineering": {
            "extract_temporal_features": True,
            "extract_spatial_features": True,
            "extract_behavioral_features": True,
            "extract_quality_features": True
        },
        "quality_assessment": {
            "quality_thresholds": {
                "high": 0.8,
                "medium": 0.5,
                "low": 0.0
            },
            "anomaly_contamination": 0.1
        },
        "output_settings": {
            "preserve_original_data": True,
            "add_usage_recommendations": True,
            "include_confidence_scores": True
        }
    }
    
    try:
        import json
        config_file = Path("ML/ml_config.json")
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"✅ Created example configuration: {config_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create configuration: {e}")
        return False

def main():
    """Main setup function."""
    print("🤖 ML-Based Touch Data Cleaning - Environment Setup")
    print("=" * 60)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Create directories
    if success and not create_directories():
        success = False
    
    # Install dependencies
    if success and not install_dependencies():
        success = False
    
    # Test imports
    if success and not test_imports():
        success = False
    
    # Test ML components
    if success and not test_ml_components():
        success = False
    
    # Create example configuration
    if success and not create_example_config():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Setup completed successfully!")
        print("=" * 60)
        print("✅ All dependencies installed")
        print("✅ ML components tested and working")
        print("✅ Directories created")
        print("✅ Example configuration created")
        print()
        print("Next steps:")
        print("1. Run: python ML/example_usage.py")
        print("2. Or use: python ML/ml_clean_coloring_data.py --help")
        print("3. Check ML/ml_config.json for configuration options")
    else:
        print("❌ Setup failed!")
        print("=" * 60)
        print("Please check the error messages above and resolve any issues.")
        print("You may need to:")
        print("- Update Python to version 3.8 or higher")
        print("- Install pip packages manually")
        print("- Check internet connectivity for package downloads")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
